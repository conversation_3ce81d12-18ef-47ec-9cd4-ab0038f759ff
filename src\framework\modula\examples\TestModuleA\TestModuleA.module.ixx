/**
 * @file TestModuleA.module.ixx
 * @brief TestModuleA - 依赖TestModule的示例模块
 *
 * 这是一个符合README.md规范的示例模块，展示模块间依赖关系：
 * 条件1：通过enable_modula_framework()在CMake中声明
 * 条件2：主模块文件命名为<目录名>.module.ixx，使用export module语法
 * 条件3：实现符合modula.concepts concept Module的生命周期管理类
 * 条件4：通过modula.registry显式注册
 *
 * 依赖关系：TestModuleA -> TestModule
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

module;

#include <iostream>
#include <string>
#include <chrono>
#include <vector>

// Include traditional C++ library header
#include "test_lib.h"

export module TestModuleA;

// 导入依赖的模块
import TestModule;

/**
 * @brief TestModuleA类 - 符合Module概念约束的生命周期管理类
 *
 * 这是主模块类，实现了README.md中条件3要求的概念约束：
 * - 符合modula.concepts concept Module的接口要求
 * - 实现完整的生命周期管理：initialize(), shutdown(), is_initialized()
 * - 依赖TestModule模块，展示模块间依赖关系
 * - 通过REGISTER_MODULE宏进行注册（条件4）
 *
 * 使用方式：
 * ```cpp
 * import TestModuleA;
 * import modula;
 *
 * auto manager = modula::initialize_framework();
 * auto* test_module_a = manager->get_module<TestModuleA>("TestModuleA");
 * ```
 */
export class TestModuleA {
public:
    /**
     * @brief 默认构造函数
     */
    TestModuleA() = default;

    /**
     * @brief 析构函数
     */
    ~TestModuleA() {
        if (initialized_) {
            shutdown();
        }
    }

    /**
     * @brief 初始化模块
     *
     * 实现Module概念要求的初始化接口
     * 注意：依赖的TestModule应该在此模块之前初始化
     * @return bool 初始化成功返回true，失败返回false
     */
    bool initialize() {
        if (initialized_) {
            std::cout << "[TestModuleA] Already initialized" << std::endl;
            return true;
        }

        std::cout << "[TestModuleA] Initializing..." << std::endl;

        // 模拟初始化过程
        initialization_time_ = std::chrono::steady_clock::now();

        // 这里可以添加实际的初始化逻辑
        // 例如：验证依赖模块状态、加载配置、初始化资源等
        std::cout << "[TestModuleA] Verifying dependencies..." << std::endl;
        
        // 模拟一些初始化工作
        std::cout << "[TestModuleA] Loading configuration..." << std::endl;
        std::cout << "[TestModuleA] Initializing resources..." << std::endl;

        initialized_ = true;
        std::cout << "[TestModuleA] Initialization completed successfully" << std::endl;
        return true;
    }

    /**
     * @brief 关闭模块
     *
     * 实现Module概念要求的关闭接口
     */
    void shutdown() {
        if (!initialized_) {
            std::cout << "[TestModuleA] Already shutdown" << std::endl;
            return;
        }

        std::cout << "[TestModuleA] Shutting down..." << std::endl;

        // 这里可以添加实际的清理逻辑
        // 例如：释放资源、关闭连接、保存状态等
        std::cout << "[TestModuleA] Releasing resources..." << std::endl;
        std::cout << "[TestModuleA] Saving state..." << std::endl;

        initialized_ = false;
        std::cout << "[TestModuleA] Shutdown completed" << std::endl;
    }

    /**
     * @brief 检查模块是否已初始化
     *
     * 实现Module概念要求的状态查询接口
     * @return bool 已初始化返回true，否则返回false
     */
    bool is_initialized() const noexcept {
        return initialized_;
    }

    /**
     * @brief 获取模块版本（扩展接口）
     * @return std::string 版本号
     */
    std::string get_version() const {
        return "1.0.0";
    }

    /**
     * @brief 获取模块依赖列表（扩展接口）
     * @return std::vector<std::string> 依赖的模块名称列表
     */
    std::vector<std::string> get_dependencies() const {
        // TestModuleA依赖TestModule
        return {"TestModule"};
    }

    /**
     * @brief 获取模块描述（扩展接口）
     * @return std::string 模块描述
     */
    std::string get_description() const {
        return "TestModuleA - Depends on TestModule, demonstrates inter-module dependencies";
    }

    /**
     * @brief 获取初始化时间（扩展接口）
     * @return std::chrono::steady_clock::time_point 初始化时间点
     */
    std::chrono::steady_clock::time_point get_initialization_time() const {
        return initialization_time_;
    }

    /**
     * @brief 执行模块特定功能（示例方法）
     * @return std::string 执行结果
     */
    std::string perform_task() const {
        if (!initialized_) {
            return "[TestModuleA] Error: Module not initialized";
        }
        
        return "[TestModuleA] Task completed successfully - demonstrating module functionality";
    }

    /**
     * @brief 演示使用传统C++库TestLib的功能
     * @return std::string 包含TestLib功能演示结果的字符串
     */
    std::string demonstrate_testlib_integration() const {
        if (!initialized_) {
            return "[TestModuleA] Error: Module not initialized";
        }

        std::string result = "[TestModuleA] TestLib Integration Demo:\n";

        // 演示数学功能
        // result += "  Math functions:\n";
        result += "    factorial(5) = " + std::to_string(testlib::math::factorial(5)) + "\n";
        // result += "    gcd(48, 18) = " + std::to_string(testlib::math::gcd(48, 18)) + "\n";
        // result += "    is_prime(17) = " + (testlib::math::is_prime(17) ? "true" : "false") + "\n";

        // // 演示字符串处理功能
        // result += "  String utilities:\n";
        // std::string test_str = "  Hello World  ";
        // result += "    trim('" + test_str + "') = '" + testlib::string_utils::trim(test_str) + "'\n";
        // result += "    to_upper('hello') = '" + testlib::string_utils::to_upper("hello") + "'\n";

        // // 演示验证功能
        // result += "  Validation functions:\n";
        // result += "    is_valid_email('<EMAIL>') = " +
        //           (testlib::validation::is_valid_email("<EMAIL>") ? "true" : "false") + "\n";
        // result += "    password_strength('MyP@ssw0rd123') = " +
        //           std::to_string(testlib::validation::password_strength("MyP@ssw0rd123")) + "/5\n";

        // // 演示性能测量
        // result += "  Performance measurement:\n";
        // testlib::performance::Timer timer;
        // timer.start();

        // // 模拟一些工作
        // volatile int sum = 0;
        // for (int i = 0; i < 100000; ++i) {
        //     sum += i;
        // }

        // double elapsed = timer.stop();
        // result += "    Computed sum of 0-99999 in " + testlib::performance::format_duration(elapsed) + "\n";

        return result;
    }

private:
    bool initialized_ = false;  ///< 初始化状态标志
    std::chrono::steady_clock::time_point initialization_time_;  ///< 初始化时间点
};
