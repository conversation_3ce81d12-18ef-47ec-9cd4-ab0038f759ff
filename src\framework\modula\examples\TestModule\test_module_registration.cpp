﻿/**
 * @file test_module_registration.cpp
 * @brief TestModule的模块注册实现 - 条件4：显式注册
 *
 * 这个文件实现了README.md中条件4的要求：
 * "主模块类需要在编译期或运行时显式注册(通过modular.registry)"
 *
 * 使用REGISTER_MODULE宏系统进行模块注册，支持完整的元数据定义
 */

// 导入必要的模块
import modular.concepts;  // 获取概念约束和注册函数
import TestModule;        // 导入主模块文件（符合条件2的命名规范）

// 包含宏定义
#include "../../includes/modular_macros.h"

// 条件4：使用REGISTER_MODULE宏注册模块元数据
// 这确保了TestModule类符合Module概念约束并在框架中正确注册
REGISTER_MODULE(TestModule, "TestModule", "3.0.0", "README.md compliant example module", 75)

// 确保静态初始化器被执行的辅助函数
namespace {
    void ensure_testmodule_registration() {
        // 这个函数的存在确保了静态初始化器被链接和执行
        static bool initialized = false;
        if (!initialized) {
            initialized = true;
            // 验证注册是否成功
            if (modular::unified_module_registry<TestModule>::is_registered()) {
                // 注册成功
            }
        }
    }

    // 在程序启动时自动调用
    static int dummy = (ensure_testmodule_registration(), 0);
}
