#!/usr/bin/env python3
"""
@file core.py
@brief Consolidated core module for Modula Generator

All core functionality including data processing, dependency analysis, and code generation.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Set, Optional


# Exception classes
class ModulaGeneratorError(Exception):
    """Base exception for all Modula Generator errors"""
    pass


class ConfigurationError(ModulaGeneratorError):
    """Configuration validation error"""
    pass


class GenerationError(ModulaGeneratorError):
    """Code generation error"""
    pass


class DependencyError(ModulaGeneratorError):
    """Dependency analysis error"""
    pass


class ValidationError(ModulaGeneratorError):
    """Data validation error"""
    pass


class TemplateError(ModulaGeneratorError):
    """Template processing error"""
    pass


# JSON Data Reader
class JsonDataReader:
    """JSON data reader for module metadata with enhanced normalization"""
    
    def __init__(self, json_file: str):
        self.json_file = json_file
        self.logger = logging.getLogger("json_reader")
    
    def read_modules(self) -> List[Dict[str, Any]]:
        """Read module data from JSON file with enhanced normalization
        
        Returns:
            List of normalized module data dictionaries
        """
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Handle different JSON structures
            if isinstance(data, list):
                raw_modules = data
            elif isinstance(data, dict):
                if 'modules' in data:
                    raw_modules = data['modules']
                elif 'targets' in data:
                    raw_modules = data['targets']
                else:
                    # Treat the dict as a single module
                    raw_modules = [data]
            else:
                raise GenerationError(f"Invalid JSON structure in {self.json_file}")
            
            # Normalize module data
            modules = self._normalize_modules(raw_modules)
            
            # Validate modules
            validated_modules = self._validate_modules(modules)
            
            self.logger.info(f"Read {len(validated_modules)} modules from {self.json_file}")
            return validated_modules
            
        except json.JSONDecodeError as e:
            raise GenerationError(f"Invalid JSON in {self.json_file}: {e}")
        except FileNotFoundError:
            raise GenerationError(f"JSON file not found: {self.json_file}")
        except Exception as e:
            raise GenerationError(f"Failed to read JSON file {self.json_file}: {e}")
    
    def _normalize_modules(self, raw_modules: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize module data to ensure consistent format"""
        normalized = []
        for module in raw_modules:
            normalized_module = {
                'name': module.get('name', module.get('target_name', 'unknown')),
                'target_name': module.get('target_name', module.get('name', 'unknown')),
                'version': module.get('version', '1.0.0'),
                'directory': module.get('directory', ''),
                'interface_file': module.get('interface_file', module.get('interface', '')),
                'dependencies': module.get('dependencies', []),
                'sources': module.get('sources', []),
                'build_config': module.get('build_config', {}),
                'output_config': module.get('output_config', {}),
                'link_info': module.get('link_info', {}),
                'metadata': module.get('metadata', {}),
                'parallel_group': module.get('parallel_group', 0),
                'priority': module.get('priority', 0)
            }
            normalized.append(normalized_module)
        return normalized
    
    def _validate_modules(self, modules: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate module data"""
        validated = []
        for module in modules:
            if self._validate_module(module):
                validated.append(module)
            else:
                self.logger.warning(f"Skipping invalid module: {module.get('name', 'unknown')}")
        return validated
    
    def _validate_module(self, module: Dict[str, Any]) -> bool:
        """Validate a single module"""
        try:
            # Check required fields
            if not module.get('name'):
                self.logger.error(f"Module missing name: {module}")
                return False
            
            # Validate dependencies are list
            if not isinstance(module.get('dependencies', []), list):
                self.logger.error(f"Module dependencies must be a list: {module['name']}")
                return False
            
            # Validate sources are list
            if not isinstance(module.get('sources', []), list):
                self.logger.error(f"Module sources must be a list: {module['name']}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Module validation failed: {e}")
            return False


# Dependency Analyzer
class DependencyAnalyzer:
    """Advanced dependency analyzer with cycle detection and parallel group support"""
    
    def __init__(self, modules: List[Dict[str, Any]]):
        self.modules = modules
        self.logger = logging.getLogger("dependency_analyzer")
        self.dependency_graph: Dict[str, List[str]] = {}
        self.reverse_graph: Dict[str, List[str]] = {}
        
    def analyze(self) -> Dict[str, Any]:
        """Perform comprehensive dependency analysis"""
        try:
            # Build dependency graphs
            self._build_graphs()
            
            # Detect cycles
            cycles = self._detect_cycles()
            if cycles:
                self.logger.error(f"Circular dependencies detected: {cycles}")
                return {
                    'has_cycles': True,
                    'cycles': cycles,
                    'initialization_order': [],
                    'parallel_groups': {},
                    'max_depth': 0
                }
            
            # Perform topological sort
            initialization_order = self._topological_sort()
            
            # Analyze parallel groups
            parallel_groups = self._analyze_parallel_groups()
            
            # Calculate dependency depth
            max_depth = self._calculate_max_depth()
            
            self.logger.info(f"Dependency analysis completed. Order: {initialization_order}")
            
            return {
                'has_cycles': False,
                'cycles': [],
                'initialization_order': initialization_order,
                'parallel_groups': parallel_groups,
                'max_depth': max_depth,
                'dependency_graph': self.dependency_graph,
                'reverse_graph': self.reverse_graph
            }
            
        except Exception as e:
            self.logger.error(f"Dependency analysis failed: {e}")
            raise DependencyError(f"Dependency analysis failed: {e}")
    
    def _build_graphs(self) -> None:
        """Build dependency and reverse dependency graphs"""
        # Initialize graphs
        for module in self.modules:
            module_name = module['name']
            self.dependency_graph[module_name] = []
            self.reverse_graph[module_name] = []
        
        # Build dependency relationships
        for module in self.modules:
            module_name = module['name']
            dependencies = module.get('dependencies', [])
            
            for dep in dependencies:
                if dep in self.dependency_graph:
                    self.dependency_graph[module_name].append(dep)
                    self.reverse_graph[dep].append(module_name)
                else:
                    self.logger.warning(f"Unknown dependency '{dep}' for module '{module_name}'")
    
    def _detect_cycles(self) -> List[List[str]]:
        """Detect circular dependencies using DFS"""
        visited = set()
        rec_stack = set()
        cycles = []
        
        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in self.dependency_graph.get(node, []):
                if dfs(neighbor, path):
                    return True
            
            rec_stack.remove(node)
            path.pop()
            return False
        
        for module_name in self.dependency_graph:
            if module_name not in visited:
                dfs(module_name, [])
        
        return cycles
    
    def _topological_sort(self) -> List[str]:
        """Perform topological sort to determine initialization order"""
        in_degree = {}
        
        # Calculate in-degrees
        for module_name in self.dependency_graph:
            in_degree[module_name] = 0
        
        for module_name, dependencies in self.dependency_graph.items():
            for dep in dependencies:
                if dep in in_degree:
                    in_degree[module_name] += 1
        
        # Kahn's algorithm
        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            # Update in-degrees
            for dependent in self.reverse_graph.get(current, []):
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)
        
        return result
    
    def _analyze_parallel_groups(self) -> Dict[int, List[str]]:
        """Analyze parallel groups based on module metadata"""
        groups = {}
        for module in self.modules:
            module_name = module['name']
            parallel_group = module.get('parallel_group', 0)
            if parallel_group not in groups:
                groups[parallel_group] = []
            groups[parallel_group].append(module_name)
        return groups
    
    def _calculate_max_depth(self) -> int:
        """Calculate maximum dependency depth"""
        def get_depth(module_name: str, visited: Set[str]) -> int:
            if module_name in visited:
                return 0  # Avoid infinite recursion
            
            visited.add(module_name)
            dependencies = self.dependency_graph.get(module_name, [])
            
            if not dependencies:
                visited.remove(module_name)
                return 1
            
            max_dep_depth = max(get_depth(dep, visited) for dep in dependencies)
            visited.remove(module_name)
            return max_dep_depth + 1
        
        max_depth = 0
        for module_name in self.dependency_graph:
            depth = get_depth(module_name, set())
            max_depth = max(max_depth, depth)
        
        return max_depth


# C++ Code Generator
class CppCodeGenerator:
    """C++ code generator with full metadata support"""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger("cpp_generator")
        self.generated_files: List[Path] = []

    def generate_all(self, modules: List[Dict[str, Any]],
                    dependency_result: Optional[Dict[str, Any]] = None) -> bool:
        """Generate all C++ files with dependency information

        Args:
            modules: List of module data
            dependency_result: Optional dependency analysis result

        Returns:
            True if generation succeeded, False otherwise
        """
        try:
            from .templates import TemplateManager

            output_path = Path(self.config.output_directory)
            output_path.mkdir(parents=True, exist_ok=True)

            template_manager = TemplateManager()

            # Generate main module interface file
            self._generate_main_interface(modules, dependency_result, output_path, template_manager)

            # Generate companion files for each module
            for module in modules:
                self._generate_companion_file(module, output_path, template_manager)

            self.logger.info(f"Generated {len(modules) + 1} files")
            return True

        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return False

    def _generate_main_interface(self, modules: List[Dict[str, Any]],
                                dependency_result: Optional[Dict[str, Any]],
                                output_path: Path, template_manager) -> None:
        """Generate main module interface file"""
        try:
            # Create template context
            context = {
                'modules': modules,
                'module_count': len(modules),
                'generated_at': self._get_timestamp(),
                'dependency_result': dependency_result
            }

            # Generate content
            content = template_manager.render_main_interface(context)

            # Write file
            main_file = output_path / self.config.main_module_filename
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(content)

            self.generated_files.append(main_file)
            self.logger.debug(f"Generated main interface: {main_file}")

        except Exception as e:
            raise GenerationError(f"Failed to generate main interface: {e}")

    def _generate_companion_file(self, module: Dict[str, Any], output_path: Path, template_manager) -> None:
        """Generate companion file for a module"""
        try:
            module_name = module.get('name', module.get('target_name', 'unknown'))

            # Create template context
            context = {
                'module': module,
                'module_name': module_name,
                'generated_at': self._get_timestamp()
            }

            # Generate content
            content = template_manager.render_companion_file(context)

            # Write file
            companion_file = output_path / f"{module_name}{self.config.companion_file_suffix}"
            with open(companion_file, 'w', encoding='utf-8') as f:
                f.write(content)

            self.generated_files.append(companion_file)
            self.logger.debug(f"Generated companion file: {companion_file}")

        except Exception as e:
            raise GenerationError(f"Failed to generate companion file for module {module}: {e}")

    def _get_timestamp(self) -> str:
        """Get current timestamp string"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def get_generated_files(self) -> List[Path]:
        """Get list of generated files"""
        return self.generated_files.copy()

    def get_statistics(self) -> Dict[str, Any]:
        """Get generation statistics"""
        return {
            'files_generated': len(self.generated_files),
            'main_interface_generated': any(
                f.name == self.config.main_module_filename
                for f in self.generated_files
            ),
            'companion_files_generated': len([
                f for f in self.generated_files
                if f.name.endswith(self.config.companion_file_suffix)
            ])
        }


# Export all classes
__all__ = [
    'JsonDataReader',
    'DependencyAnalyzer',
    'CppCodeGenerator',
    'ModulaGeneratorError',
    'ConfigurationError',
    'GenerationError',
    'DependencyError',
    'ValidationError',
    'TemplateError'
]
