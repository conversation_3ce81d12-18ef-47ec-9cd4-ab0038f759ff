# Modula - 现代化的C++20/23模块化框架

`Modula` 是一个基于C++20/23的现代化模块化框架，旨在为复杂的C++应用程序提供强大的架构管理、依赖分析和生命周期控制能力。它通过将软件解构为一系列定义明确、高内聚的模块（Module），为构建健壮、可维护且可扩展的系统提供了一套完整的工具链和运行时支持。

`Modula` 的核心哲学是 **将高层级的软件架构信息融入构建系统，并在编译期预处理，从而为运行时提供零开销、高度优化的模块管理服务**。

> **重要概念区分**: `Modula` 中的 **模块（Module）** 是一个 **软件架构** 层面的概念，它代表应用中的一个逻辑功能单元（如子系统、服务或功能库）。此概念完全独立于C++20语言层面的 **代码模块（`export module`）**。无论您使用传统头文件还是C++20模块组织代码，`Modula` 都能帮助您管理软件架构。

## 工作流：从构建到运行

`Modula` 的核心是一个高度自动化的处理流水线，它在CMake配置阶段分析您的代码，生成元数据，并将这些元数据编译到您的程序中。

1.  **声明 (Declaration)**: 在 `CMakeLists.txt` 中，您只需使用 `declare_module()` 函数，将一个CMake目标（通常是一个库）声明为一个Modula模块。
    ```cmake
    # my_module/CMakeLists.txt
    add_library(MyModule)
    target_sources(MyModule PUBLIC FILE_SET CXX_MODULES FILES MyModule.module.ixx)
    
    # 将MyModule声明为Modula模块
    declare_module(MyModule)
    ```

2.  **发现与分析 (Discovery & Analysis)**: 在CMake配置期间，`Modula`的处理器会自动运行：
    *   **`DiscoveryProcessor`**: 根据命名约定（如 `MyModule.module.ixx`）找到模块的主接口文件。
    *   **`AnalyzerProcessor`**: 使用正则表达式解析主接口文件，扫描 `import` 和 `export module` 语句，以确定模块间的依赖关系。

3.  **元数据生成 (Metadata Generation)**:
    *   **`MetadataProcessor`**: 将所有分析收集到的信息（模块名、依赖项、文件路径等）汇总，生成一个 `modules_metadata.json` 文件。这个文件是整个系统架构的快照。

4.  **代码生成 (Code Generation)**:
    *   **`GeneratorProcessor`**: 调用内部的Python脚本，读取 `modules_metadata.json` 文件。
    *   该脚本将JSON中描述的架构信息，动态生成为C++源代码（如 `modula.generated.ixx`）。这些生成的代码包含了编译期可用的静态元数据（如模块名称列表、依赖关系矩阵等）。

5.  **编译 (Compilation)**:
    *   生成的C++元数据文件会被编译到一个名为 `modula_generated` 的静态库中。
    *   您的主程序只需链接 `modula` 框架库，`modula_generated` 会被自动链接，从而将所有架构信息无缝地嵌入到最终的可执行文件中。

6.  **运行 (Runtime)**:
    *   应用程序启动时，`ModuleManager` 开始工作。
    *   它向 `DependencyGraph` 请求模块初始化顺序。
    *   `DependencyGraph` 直接从编译期生成的静态元数据中读取依赖关系，无需任何运行时文件IO或复杂的解析，实现了 **零成本的依赖分析**。
    *   `DependencyGraph` 执行拓扑排序，检测循环依赖，并返回一个安全的初始化序列。
    *   `ModuleManager` 按照此序列，依次安全地初始化所有模块。

## 核心功能

*   **声明式架构**: 在CMake中以声明方式定义模块，将架构意图与构建系统深度融合。
*   **自动化依赖分析**: 在CMake配置阶段自动解析C++20模块的`import`语句，构建依赖图，无需手动维护依赖列表。
*   **编译期元数据**: 将分析出的依赖关系和模块信息在编译时生成为C++代码，实现运行时的零开销读取。
*   **拓扑排序与循环依赖检测**: 在运行时基于编译期生成的元数据，使用高效的图算法对模块进行拓扑排序，并能精确报告循环依赖。
*   **健壮的生命周期管理**: `ModuleManager` 确保所有模块按照正确的依赖顺序进行初始化和逆序关闭，保证资源的有序释放。
*   **非侵入式集成**: 对现有项目友好，只需修改CMake文件即可逐步引入，对业务逻辑代码无侵入。
*   **可扩展的处理器流水线**: 框架的分析、生成等步骤由独立的处理器构成，易于扩展以支持新的功能或元数据格式。

## 快速上手

1.  **定义模块接口** (`LoggingModule.module.ixx`)
    ```cpp
    export module LoggingModule;
    
    import modula.types;
    
    export class LoggingModule : public modula::IModule
    {
    public:
        void initialize(const modula::ModuleContext& context) override {
            // 初始化日志服务...
        }
    
        void shutdown() override {
            // 关闭日志服务...
        }
    };
    ```

2.  **注册模块实现** (`logging_module_registration.cpp`)
    ```cpp
    #include "modular_macros.h"
    #include "LoggingModule.module.ixx"
    
    REGISTER_MODULE(LoggingModule);
    ```

3.  **在CMake中声明模块** (`CMakeLists.txt`)
    ```cmake
    # 创建一个库
    add_library(LoggingModule)
    
    # 添加源文件，包括模块接口和注册文件
    target_sources(LoggingModule 
        PUBLIC FILE_SET CXX_MODULES FILES LoggingModule.module.ixx
        PRIVATE logging_module_registration.cpp
    )
    
    # 链接modula框架
    target_link_libraries(LoggingModule PUBLIC modula)
    
    # 将其声明为Modula模块
    declare_module(LoggingModule)
    ```

## 框架模块职责

| 模块 | 核心职责 | 主要组件/功能 | 
|------------------------------|--------------------|-------------------------------------------------|
| **`modula`** | 框架主入口 | 统一导出所有公共接口 |
| **`modula.types`** | 基础类型定义 | `IModule`, `ModuleContext`, `ModuleInfo` 等核心类型 |
| **`modula.metadata`** | 编译期元数据 | 提供对`modula_generated`库中静态元数据的访问接口 |
| **`modula.registry`** | 模块注册 | 提供 `REGISTER_MODULE` 宏和运行时的模块实例注册表 |
| **`modula.dependency_graph`**| 依赖图与分析 | `DependencyGraph` 类，拓扑排序，循环依赖检测 |
| **`modula.manager`** | 生命周期管理 | `ModuleManager` 类，负责模块的初始化与关闭流程控制 |
| **`modula.info`** | 模块信息封装 | 提供模块信息的运行时表示 |

## 系统要求

- **C++20/23 兼容编译器**: 
  - MSVC 2022 (v17.0+)
  - Clang 16+
  - GCC 15+ (实验性)
- **CMake 3.28+**: 需要支持C++模块的构建系统API。
- **Python 3.8+**: 用于代码生成器。
- **操作系统**: Windows, Linux, macOS。

