/**
 * @file modula.dependency_graph.cpp
 * @brief Modula Framework - 依赖图模块实现
 *
 * 包含依赖图管理的复杂算法实现，遵循接口实现分离原则
 * 提供高效的图算法和异常安全的依赖分析
 *
 * @version 3.0.0
 * <AUTHOR> Framework Team
 */

module;

#include <algorithm>
#include <queue>
#include <stack>
#include <set>
#include <functional>
#include <sstream>
#include <iomanip>

module modula.dependency_graph;

namespace modula {

/**
 * @brief DependencyGraph的复杂实现函数
 */

/**
 * @brief 递归收集传递依赖 - 增强版本支持循环检测
 *
 * @param module_name 模块名称
 * @param result 结果容器
 * @param visited 已访问集合
 * @param recursion_stack 递归栈，用于循环检测
 */
void DependencyGraph::collect_transitive_dependencies(const std::string& module_name,
                                                      std::vector<std::string>& result,
                                                      std::unordered_set<std::string>& visited,
                                                      std::unordered_set<std::string>& recursion_stack) const {
    if (visited.find(module_name) != visited.end()) {
        return;
    }

    visited.insert(module_name);
    recursion_stack.insert(module_name);

    const auto it = nodes_.find(module_name);
    if (it != nodes_.end()) {
        for (const auto& dep : it->second.dependencies) {
            result.push_back(dep);
            collect_transitive_dependencies(dep, result, visited, recursion_stack);
        }
    }

    recursion_stack.erase(module_name);
}

/**
 * @brief 使用Kahn算法进行拓扑排序的完整实现
 *
 * 支持优先级排序和异常安全的拓扑排序算法
 * 使用优先队列确保高优先级模块优先初始化
 */
void DependencyGraph::topological_sort() {
    // 创建入度副本
    std::unordered_map<std::string, size_t> in_degree_copy;
    for (const auto& [name, node] : nodes_) {
        in_degree_copy[name] = node.in_degree;
    }

    // 使用优先队列按优先级排序
    auto compare = [this](const std::string& a, const std::string& b) {
        const auto it_a = nodes_.find(a);
        const auto it_b = nodes_.find(b);
        if (it_a != nodes_.end() && it_b != nodes_.end()) {
            // 高优先级先处理（数值越大优先级越高）
            if (it_a->second.priority != it_b->second.priority) {
                return it_a->second.priority < it_b->second.priority;
            }
        }
        return a > b; // 字典序作为次要排序
    };

    std::priority_queue<std::string, std::vector<std::string>, decltype(compare)> queue(compare);

    // 将所有入度为0的节点加入队列
    for (const auto& [name, node] : nodes_) {
        if (in_degree_copy[name] == 0) {
            queue.push(name);
        }
    }

    analysis_.initialization_order.clear();
    analysis_.initialization_order.reserve(nodes_.size());

    while (!queue.empty()) {
        const std::string current = queue.top();
        queue.pop();

        analysis_.initialization_order.push_back(current);

        // 更新依赖该节点的所有节点的入度
        const auto it = nodes_.find(current);
        if (it != nodes_.end()) {
            for (const auto& dependent : it->second.dependents) {
                --in_degree_copy[dependent];
                if (in_degree_copy[dependent] == 0) {
                    queue.push(dependent);
                }
            }
        }
    }

    // 验证拓扑排序的完整性
    if (analysis_.initialization_order.size() != nodes_.size()) {
        throw DependencyGraphException("Topological sort incomplete - possible circular dependencies");
    }

    // 生成关闭顺序（初始化顺序的逆序）
    analysis_.shutdown_order = analysis_.initialization_order;
    std::reverse(analysis_.shutdown_order.begin(), analysis_.shutdown_order.end());
}

/**
 * @brief 计算模块层级
 */
void DependencyGraph::calculate_levels() {
    analysis_.levels.clear();

    // 使用BFS计算每个模块的层级
    std::queue<std::string> queue;
    std::unordered_map<std::string, bool> visited;

    // 找到所有根节点（无依赖的节点）
    for (const auto& [name, node] : nodes_) {
        if (node.dependencies.empty()) {
            queue.push(name);
            analysis_.levels[name] = 0;
            visited[name] = true;
        }
    }

    while (!queue.empty()) {
        std::string current = queue.front();
        queue.pop();

        size_t current_level = analysis_.levels[current];

        // 处理所有依赖当前节点的节点
        auto it = nodes_.find(current);
        if (it != nodes_.end()) {
            for (const auto& dependent : it->second.dependents) {
                if (!visited[dependent]) {
                    // 检查该节点的所有依赖是否都已处理
                    bool all_deps_processed = true;
                    size_t max_dep_level = 0;

                    auto dep_it = nodes_.find(dependent);
                    if (dep_it != nodes_.end()) {
                        for (const auto& dep : dep_it->second.dependencies) {
                            if (!visited[dep]) {
                                all_deps_processed = false;
                                break;
                            }
                            max_dep_level = std::max(max_dep_level, analysis_.levels[dep]);
                        }
                    }

                    if (all_deps_processed) {
                        analysis_.levels[dependent] = max_dep_level + 1;
                        visited[dependent] = true;
                        queue.push(dependent);
                    }
                }
            }
        }
    }
}

/**
 * @brief 查找孤立模块
 */
void DependencyGraph::find_orphaned_modules() {
    analysis_.orphaned_modules.clear();

    for (const auto& [name, node] : nodes_) {
        // 孤立模块：既没有依赖也没有被依赖
        if (node.dependencies.empty() && node.dependents.empty()) {
            analysis_.orphaned_modules.push_back(name);
        }
    }
}

/**
 * @brief 高级图分析功能
 */

/**
 * @brief 计算图的聚类系数
 * @return 聚类系数 (0.0 - 1.0)
 */
double DependencyGraph::calculate_clustering_coefficient() const {
    if (nodes_.size() < 3) {
        return 0.0;
    }

    double total_coefficient = 0.0;
    size_t valid_nodes = 0;

    for (const auto& [name, node] : nodes_) {
        if (node.dependencies.size() < 2) {
            continue; // 需要至少2个依赖才能计算聚类系数
        }

        // 计算该节点的局部聚类系数
        size_t possible_edges = node.dependencies.size() * (node.dependencies.size() - 1) / 2;
        size_t actual_edges = 0;

        // 检查依赖之间是否存在连接
        for (size_t i = 0; i < node.dependencies.size(); ++i) {
            for (size_t j = i + 1; j < node.dependencies.size(); ++j) {
                auto dep1_it = nodes_.find(node.dependencies[i]);
                if (dep1_it != nodes_.end()) {
                    auto& dep1_deps = dep1_it->second.dependencies;
                    if (std::find(dep1_deps.begin(), dep1_deps.end(), node.dependencies[j]) != dep1_deps.end()) {
                        ++actual_edges;
                    }
                }
            }
        }

        if (possible_edges > 0) {
            total_coefficient += static_cast<double>(actual_edges) / possible_edges;
            ++valid_nodes;
        }
    }

    return valid_nodes > 0 ? total_coefficient / valid_nodes : 0.0;
}

/**
 * @brief 查找关键路径（最长依赖链）
 * @return 关键路径上的模块列表
 */
std::vector<std::string> DependencyGraph::find_critical_path() const {
    std::vector<std::string> critical_path;

    if (nodes_.empty()) {
        return critical_path;
    }

    // 使用动态规划找到最长路径
    std::unordered_map<std::string, int> longest_path;
    std::unordered_map<std::string, std::string> predecessor;

    // 初始化
    for (const auto& [name, node] : nodes_) {
        longest_path[name] = 0;
    }

    // 按拓扑顺序处理节点
    for (const auto& node_name : analysis_.initialization_order) {
        auto it = nodes_.find(node_name);
        if (it != nodes_.end()) {
            for (const auto& dependent : it->second.dependents) {
                int new_path_length = longest_path[node_name] + 1;
                if (new_path_length > longest_path[dependent]) {
                    longest_path[dependent] = new_path_length;
                    predecessor[dependent] = node_name;
                }
            }
        }
    }

    // 找到路径最长的节点
    std::string end_node;
    int max_length = -1;
    for (const auto& [name, length] : longest_path) {
        if (length > max_length) {
            max_length = length;
            end_node = name;
        }
    }

    // 重构路径
    if (!end_node.empty()) {
        std::string current = end_node;
        while (!current.empty()) {
            critical_path.push_back(current);
            auto pred_it = predecessor.find(current);
            current = (pred_it != predecessor.end()) ? pred_it->second : "";
        }
        std::reverse(critical_path.begin(), critical_path.end());
    }

    return critical_path;
}

/**
 * @brief 检测强连通分量
 * @return 强连通分量列表
 */
std::vector<std::vector<std::string>> DependencyGraph::find_strongly_connected_components() const {
    std::vector<std::vector<std::string>> sccs;
    std::unordered_map<std::string, bool> visited;
    std::unordered_map<std::string, bool> on_stack;
    std::unordered_map<std::string, int> ids;
    std::unordered_map<std::string, int> low_links;
    std::stack<std::string> stack;
    int id_counter = 0;

    // Tarjan算法实现
    std::function<void(const std::string&)> tarjan = [&](const std::string& node) {
        stack.push(node);
        on_stack[node] = true;
        ids[node] = low_links[node] = id_counter++;
        visited[node] = true;

        auto it = nodes_.find(node);
        if (it != nodes_.end()) {
            for (const auto& dep : it->second.dependencies) {
                if (!visited[dep]) {
                    tarjan(dep);
                }
                if (on_stack[dep]) {
                    low_links[node] = std::min(low_links[node], low_links[dep]);
                }
            }
        }

        // 如果node是SCC的根
        if (ids[node] == low_links[node]) {
            std::vector<std::string> scc;
            std::string w;
            do {
                w = stack.top();
                stack.pop();
                on_stack[w] = false;
                scc.push_back(w);
            } while (w != node);
            sccs.push_back(scc);
        }
    };

    for (const auto& [name, node] : nodes_) {
        if (!visited[name]) {
            tarjan(name);
        }
    }

    return sccs;
}

/**
 * @brief 生成模块影响分析报告
 * @param module_name 要分析的模块名称
 * @return 影响分析报告
 */
std::string DependencyGraph::generate_impact_analysis(const std::string& module_name) const {
    std::ostringstream report;
    report << "=== Module Impact Analysis: " << module_name << " ===\n\n";

    auto it = nodes_.find(module_name);
    if (it == nodes_.end()) {
        report << "Module not found in dependency graph.\n";
        return report.str();
    }

    const auto& node = it->second;

    // 直接依赖
    report << "Direct Dependencies (" << node.dependencies.size() << "):\n";
    for (const auto& dep : node.dependencies) {
        report << "  - " << dep << "\n";
    }

    // 直接被依赖
    report << "\nDirect Dependents (" << node.dependents.size() << "):\n";
    for (const auto& dependent : node.dependents) {
        report << "  - " << dependent << "\n";
    }

    // 传递依赖
    auto transitive_deps = get_transitive_dependencies(module_name);
    report << "\nTransitive Dependencies (" << transitive_deps.size() << "):\n";
    for (const auto& dep : transitive_deps) {
        report << "  - " << dep << "\n";
    }

    // 影响范围（所有会受到该模块变化影响的模块）
    std::set<std::string> impact_set;
    std::function<void(const std::string&)> collect_impact = [&](const std::string& name) {
        auto node_it = nodes_.find(name);
        if (node_it != nodes_.end()) {
            for (const auto& dependent : node_it->second.dependents) {
                if (impact_set.insert(dependent).second) {
                    collect_impact(dependent);
                }
            }
        }
    };

    collect_impact(module_name);

    report << "\nImpact Scope (" << impact_set.size() << " modules affected):\n";
    for (const auto& affected : impact_set) {
        report << "  - " << affected << "\n";
    }

    // 层级信息
    auto level_it = analysis_.levels.find(module_name);
    if (level_it != analysis_.levels.end()) {
        report << "\nModule Level: " << level_it->second << "\n";
    }

    // 关键性分析
    double criticality = static_cast<double>(impact_set.size()) / nodes_.size();
    report << "Criticality Score: " << std::fixed << std::setprecision(2) << criticality << "\n";

    if (criticality > 0.5) {
        report << "⚠️  HIGH IMPACT: Changes to this module will affect more than 50% of the system.\n";
    } else if (criticality > 0.2) {
        report << "⚡ MEDIUM IMPACT: Changes to this module will affect a significant portion of the system.\n";
    } else {
        report << "✅ LOW IMPACT: Changes to this module have limited system-wide effects.\n";
    }

    report << "\n" << std::string(50, '=') << "\n";

    return report.str();
}

} // namespace modula
