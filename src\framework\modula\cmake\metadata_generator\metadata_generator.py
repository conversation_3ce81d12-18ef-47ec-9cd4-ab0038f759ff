#!/usr/bin/env python3
"""
Modula Framework - 元数据生成器主控制器

简化的元数据生成系统，专注于核心功能：
- 读取CMake生成的JSON元数据文件
- 生成C++编译期元数据代码
- 生成JSON格式的元数据输出

使用方式：
    python metadata_generator.py --json-file metadata.json --output-dir output
    python metadata_generator.py --json-file metadata.json --output-dir output --verbose

版本: 3.0.0 (重构简化版)
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加模块路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from configuration import ConfigurationManager, setup_logging
from processing_pipeline import MetadataProcessor


class MetadataGeneratorApp:
    """简化的主应用程序类"""

    def __init__(self):
        self.config_manager = ConfigurationManager()
        self.processor = None
        self.logger = logging.getLogger("app")

    def initialize(self, args) -> bool:
        """初始化应用程序"""
        try:
            # 加载配置
            self.config_manager.load_from_args(args)

            # 设置日志
            setup_logging(self.config_manager.get_config().verbose)

            # 验证配置
            if not self.config_manager.validate_config():
                return False

            # 初始化处理器
            self.processor = MetadataProcessor(self.config_manager.get_config())

            return True

        except Exception as e:
            print(f"Initialization failed: {e}")
            return False

    def run(self) -> bool:
        """运行元数据生成"""
        try:
            config = self.config_manager.get_config()

            self.logger.info(f"Starting metadata generation from: {config.json_file}")
            self.logger.info(f"Output directory: {config.output_directory}")

            # 执行处理
            success = self.processor.process()

            if success:
                self.logger.info("Metadata generation completed successfully")
                if config.verbose:
                    self._print_summary()
            else:
                self.logger.error("Metadata generation failed")

            return success

        except Exception as e:
            self.logger.error(f"Metadata generation failed: {e}")
            return False

    def _print_summary(self) -> None:
        """打印生成摘要"""
        stats = self.processor.get_statistics()
        print("\n=== Generation Summary ===")
        print(f"Modules processed: {stats.get('modules_processed', 0)}")
        print(f"Files generated: {stats.get('files_generated', 0)}")
        print(f"Processing time: {stats.get('processing_time', 0):.3f}s")
        print("==========================\n")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建简化的命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Modula Framework - 元数据生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # 基本用法
  python metadata_generator.py --json-file metadata.json --output-dir output

  # 详细输出模式
  python metadata_generator.py --json-file metadata.json --output-dir output --verbose
        """
    )

    # 必需参数
    parser.add_argument("--json-file", "-j", required=True,
                       help="JSON metadata file path (required)")

    # 可选参数
    parser.add_argument("--output-dir", "-o", default="modula_generated",
                       help="Output directory for generated files (default: modula_generated)")

    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")

    parser.add_argument("--generate-companion-files", action="store_true", default=True,
                       help="Generate .gen.cpp files (default behavior)")

    return parser


def main() -> int:
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建应用程序实例
    app = MetadataGeneratorApp()

    # 初始化应用程序
    if not app.initialize(args):
        return 1

    # 运行元数据生成
    if app.run():
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(main())


