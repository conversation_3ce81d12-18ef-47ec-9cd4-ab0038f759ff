# Modula Generator (增强重构版)

增强的 C++ 元数据生成工具，用于 Modula Framework，与 metadata_generator 功能完全兼容。

## 概述

Modula Generator 是一个重构后的独立 Python 工具，用于从 JSON 元数据文件生成 C++ 模块注册代码。重构后的版本不仅保持了核心功能，还增强了依赖分析和元数据支持，与 metadata_generator 完全兼容。

## 主要功能

- ✅ **JSON 数据读取**：解析 CMake 生成的 JSON 元数据文件
- ✅ **高级依赖分析**：分析模块间依赖关系，检测循环依赖，支持拓扑排序
- ✅ **并行组支持**：支持并行初始化组和优先级设置
- ✅ **C++ 代码生成**：生成现代 C++20/23 模块注册代码，与 metadata_generator 格式完全一致
- ✅ **完整元数据支持**：支持编译期元数据、类型化功能、MetadataRegistry 集成
- ✅ **模块化架构**：清晰的模块分离，易于维护和扩展
- ✅ **命令行工具**：简化的命令行接口和 API
- ✅ **错误处理**：完善的错误报告和验证

## 重构改进

- 🔧 **功能完整性**：与 metadata_generator 功能完全一致，生成相同格式的文件
- 🔧 **模块化架构**：核心功能整合到 core.py，结构清晰
- 🔧 **减少依赖**：仅使用Python标准库，无外部依赖
- 🔧 **提高可维护性**：代码更简洁，更容易理解和维护
- 🔧 **保持兼容性**：API保持向后兼容，支持所有原有功能

## 安装

### 从源码安装

```bash
cd tools/modula_generator
pip install -e .
```

### 开发模式安装

```bash
cd tools/modula_generator
pip install -e ".[dev]"
```

## 使用方式

### 命令行工具

#### 基本用法

```bash
# 基本生成
modula-generator --json-file metadata.json --output-dir output

# 详细输出模式
modula-generator --json-file metadata.json --output-dir output --verbose

# 预览模式（不实际生成文件）
modula-generator --json-file metadata.json --output-dir output --preview

# 仅验证输入文件
modula-generator --json-file metadata.json --validate-only
```

#### 高级选项

```bash
# 强制覆盖现有文件
modula-generator --json-file metadata.json --output-dir output --force-overwrite

# 启用并行处理
modula-generator --json-file metadata.json --output-dir output --enable-parallel --max-workers 8

# 使用自定义模板
modula-generator --json-file metadata.json --output-dir output --template-dir ./custom_templates

# 调试模式
modula-generator --json-file metadata.json --output-dir output --debug
```

### Python API

#### 基本 API

```python
from modula_generator import ModulaGenerator, Config

# 创建配置
config = Config(
    json_file="metadata.json",
    output_directory="output",
    verbose=True
)

# 创建生成器并执行
generator = ModulaGenerator()
success = generator.generate(config)

if success:
    print("Generation completed successfully!")
    
    # 获取统计信息
    stats = generator.get_statistics()
    print(f"Modules processed: {stats['modules_processed']}")
    print(f"Files generated: {stats['files_generated']}")
```

#### 便捷函数

```python
from modula_generator import generate_from_json

# 一行生成
success = generate_from_json("metadata.json", "output", verbose=True)
```

#### 高级 API

```python
from modula_generator import ModulaGenerator, Config

generator = ModulaGenerator()

# 验证输入文件
errors = generator.validate_json_file("metadata.json")
if errors:
    for error in errors:
        print(f"Error: {error}")

# 预览生成结果
config = Config(json_file="metadata.json", output_directory="output")
preview = generator.preview_generation(config)
print(f"Will generate {preview['estimated_file_count']} files")

# 获取模块信息
generator.generate(config)
modules = generator.get_modules()
for module in modules:
    print(f"Module: {module.name}, Dependencies: {module.dependencies}")
```

## 配置选项

### 基本配置

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `json_file` | str | 必需 | JSON 元数据文件路径 |
| `output_directory` | str | "modula_generated" | 输出目录 |
| `verbose` | bool | False | 启用详细输出 |
| `debug_mode` | bool | False | 启用调试模式 |

### 生成选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `generate_companion_files` | bool | True | 生成 .gen.cpp 文件 |
| `force_overwrite` | bool | False | 强制覆盖现有文件 |
| `backup_existing` | bool | False | 备份现有文件 |

### 性能选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_parallel_processing` | bool | False | 启用并行处理 |
| `max_workers` | int | 4 | 最大工作线程数 |

### 模板选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `custom_template_dir` | str | None | 自定义模板目录 |

### 文件命名选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `main_module_filename` | str | "modula.generated.ixx" | 主模块文件名 |
| `companion_file_suffix` | str | ".gen.cpp" | 伴生文件后缀 |
| `json_metadata_filename` | str | "modules_metadata.json" | JSON 元数据文件名 |

## 环境变量支持

工具支持通过环境变量配置：

```bash
export MODULA_JSON_FILE="metadata.json"
export MODULA_OUTPUT_DIR="output"
export MODULA_VERBOSE="true"
export MODULA_DEBUG="true"
export MODULA_FORCE_OVERWRITE="true"
export MODULA_ENABLE_PARALLEL="true"
export MODULA_MAX_WORKERS="8"
export MODULA_TEMPLATE_DIR="./templates"
```

## JSON 输入格式

工具接受以下格式的 JSON 输入：

```json
{
    "metadata": {
        "source_dir": "/path/to/source",
        "binary_dir": "/path/to/build",
        "generation_time": "2024-01-01T00:00:00"
    },
    "modules": [
        {
            "name": "MyModule",
            "target_name": "MyModule",
            "version": "1.0.0",
            "directory": "src/modules/MyModule",
            "interface_file": "MyModule.module.ixx",
            "dependencies": ["CoreModule"],
            "sources": [
                "src/modules/MyModule/impl.cpp"
            ],
            "build_config": {
                "target_name": "MyModule"
            },
            "metadata": {}
        }
    ]
}
```

## 生成的文件

### 主模块文件 (modula.generated.ixx)

```cpp
export module modula.generated;

import modula.types;
import modula.metadata;
import modula.manager;

export namespace modula::generated {
    // 模块名称数组
    static constexpr const char* module_names[] = {
        "CoreModule",
        "MyModule"
    };
    
    // 初始化顺序
    constexpr std::span<const char* const> get_initialization_order() noexcept;
    
    // 其他生成的函数...
}
```

### 伴生文件 (ModuleName.gen.cpp)

```cpp
#include "modular_macros.h"
import MyModule;

namespace modula::generated {
    static constexpr const char* MyModule_dependency_names[] = {
        "CoreModule"
    };
    
    static constexpr ModuleMetadata MyModule_metadata = {
        .name = "MyModule",
        .version = "1.0.0",
        // ...其他元数据
    };
}

MODULA_REGISTER_MODULE_METADATA(MyModule, modula::generated::MyModule_metadata);
```

## 错误处理

工具提供详细的错误报告：

```bash
# 验证失败示例
$ modula-generator --json-file invalid.json --validate-only
✗ JSON file validation failed: invalid.json
  Error: No valid modules found in JSON file
  Error: Invalid module data for: InvalidModule

# 循环依赖检测
$ modula-generator --json-file circular.json --preview
⚠️  Circular dependencies detected!
   ModuleA -> ModuleB -> ModuleC -> ModuleA
```

## 与 CMake 集成

工具已集成到 Modula Framework 的 CMake 构建系统中：

```cmake
# GeneratorProcessor.cmake 会自动调用 modula_generator
modula_process_generator(${module_targets})
```

CMake 集成的数据流：

```
CMake MetadataProcessor → JSON 文件 → modula_generator → C++ 代码
```

## 自定义模板

### 创建自定义模板

1. 创建模板目录：
```bash
mkdir custom_templates
```

2. 创建模板文件 (`module.template`)：
```cpp
// 自定义模块模板
/**
 * @file ${module_name}.custom.cpp
 * @brief Custom template for ${module_name}
 */

// 你的自定义模板内容...
```

3. 使用自定义模板：
```bash
modula-generator --json-file metadata.json --output-dir output --template-dir custom_templates
```

### 模板变量

模板中可以使用以下变量：

| 变量 | 说明 |
|------|------|
| `${module_name}` | 模块名称 |
| `${target_name}` | 目标名称 |
| `${version}` | 版本信息 |
| `${generation_time}` | 生成时间 |
| `${generator_version}` | 生成器版本 |
| `${dependency_names_array}` | 依赖名称数组 |
| `${source_files_array}` | 源文件数组 |

## 开发

### 运行测试

```bash
# 安装测试依赖
pip install -e ".[test]"

# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=modula_generator
```

### 代码格式化

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 格式化代码
black modula_generator/

# 检查代码风格
flake8 modula_generator/
```

### 项目结构

```
modula_generator/
├── __init__.py          # 包初始化和公共接口
├── __main__.py          # 命令行入口点
├── core.py              # 核心生成器类
├── config.py            # 配置管理
├── processing.py        # 数据处理和代码生成
├── templates.py         # 模板管理系统
├── exceptions.py        # 自定义异常
├── setup.py            # 安装脚本
└── README.md           # 文档
```

## 版本历史

### v1.0.0 (当前版本)
- ✅ 独立工具重构
- ✅ 现代 Python 3.8+ 支持
- ✅ 完整的命令行接口
- ✅ 灵活的配置系统
- ✅ 详细的错误处理
- ✅ 与 CMake 集成

## 许可证

MIT License

## 贡献

欢迎贡献！请参阅 [CONTRIBUTING.md](../../CONTRIBUTING.md) 了解详细信息。 