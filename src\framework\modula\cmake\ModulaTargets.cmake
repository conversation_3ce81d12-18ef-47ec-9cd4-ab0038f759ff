# ==============================================================================
# ModulaTargets.cmake - Main Library Target Configuration
# ==============================================================================
#
# Defines and configures the main Modula Framework library targets.
# Handles library creation, source file configuration, and target properties.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

# Ensure compiler configuration is loaded
include(${CMAKE_CURRENT_LIST_DIR}/ModulaCompiler.cmake)

# ==============================================================================
# Main Library Target Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_create_main_library

  Create and configure the main Modula library target.

  Creates the main 'modula' library target with all necessary
  source files, properties, and configurations.
#]=======================================================================]
function(modula_create_main_library)
    # Create the main Modula library target
    add_library(modula)
    add_library(Modula::Modula ALIAS modula)

    # Configure include directories
    target_include_directories(modula
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/includes
        PUBLIC
            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/includes>
            $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
    )

    # Configure C++ module interface files
    target_sources(modula
        PUBLIC
            FILE_SET CXX_MODULES TYPE CXX_MODULES
            BASE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/modules
            FILES
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.info.ixx
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.ixx
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.manager.ixx
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.metadata.ixx
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.registry.ixx
                ${CMAKE_CURRENT_SOURCE_DIR}/modules/modula.types.ixx
    )

    # Configure public header files
    target_sources(modula
        PUBLIC
            FILE_SET HEADERS TYPE HEADERS
            BASE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/includes
            FILES
                includes/modular_macros.h
    )

    # Configure private implementation files
    target_sources(modula
        PRIVATE
            src/modula.manager.cpp
            src/modula.metadata.cpp
            src/modula.registry.cpp
    )

    # Set target properties
    set_target_properties(modula PROPERTIES
        EXPORT_NAME Modula
        VERSION ${PROJECT_VERSION}
        SOVERSION ${PROJECT_VERSION_MAJOR}
        OUTPUT_NAME "modula"
        LINKER_LANGUAGE CXX
    )

    # Apply compiler configuration
    modula_configure_target_compiler(modula)

    # Add version and feature definitions
    target_compile_definitions(modula
        PUBLIC
            MODULA_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
            MODULA_VERSION_MINOR=${PROJECT_VERSION_MINOR}
            MODULA_VERSION_PATCH=${PROJECT_VERSION_PATCH}
            MODULA_CXX_STANDARD=20
        PRIVATE
            MODULA_BUILDING_LIBRARY=1
    )

    message(STATUS "Modula: Main library target 'modula' created successfully")
endfunction()

# ==============================================================================
# Framework Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_configure_framework

  Configure main framework settings.

  Sets up metadata directory paths and JSON file configurations.
#]=======================================================================]
function(modula_configure_framework)
    if(NOT TARGET modula)
        message(FATAL_ERROR "modula_configure_framework: Target 'modula' must exist")
    endif()

    # Get configuration values from configuration system
    if(COMMAND modula_get_config)
        modula_get_config(MODULA_OUTPUT_DIR metadata_dir)
        modula_get_config(MODULA_METADATA_JSON_FILENAME json_filename)

        # Configure metadata path definitions
        target_compile_definitions(modula PRIVATE
            MODULA_METADATA_DIR_PATH="${metadata_dir}"
            MODULA_MODULES_JSON_PATH="${metadata_dir}/${json_filename}"
        )
    endif()
endfunction()

# ==============================================================================
# Target Validation
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_validate_main_target

  Validate the main library target configuration.

  Performs validation checks on the main library target.
#]=======================================================================]
function(modula_validate_main_target)
    if(NOT TARGET modula)
        message(FATAL_ERROR "modula_validate_main_target: Main target 'modula' does not exist")
    endif()

    # Validate target properties
    get_target_property(target_type modula TYPE)
    if(NOT target_type MATCHES ".*_LIBRARY")
        message(WARNING "modula_validate_main_target: Target 'modula' is not a library")
    endif()

    # Validate alias target exists
    if(NOT TARGET Modula::Modula)
        message(FATAL_ERROR "modula_validate_main_target: Alias 'Modula::Modula' does not exist")
    endif()

    message(STATUS "Modula: Main target validation completed successfully")
endfunction()

# ==============================================================================
# Target Creation and Configuration
# ==============================================================================

# Create and configure main library target
modula_create_main_library()

# Configure main framework settings
modula_configure_framework()

# Validate target configuration
modula_validate_main_target()

message(STATUS "Modula: Main library target configuration completed")
