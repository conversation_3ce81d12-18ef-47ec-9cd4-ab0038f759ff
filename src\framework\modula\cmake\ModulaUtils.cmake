# ==============================================================================
# ModulaUtils.cmake - Essential Utility Functions
# ==============================================================================
#
# Provides essential utility functions for the Modula Framework.
# Focuses on core functionality needed by framework components.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
ModulaUtils - Essential Utility Functions
=========================================

Provides core utility functions for the Modula Framework.

Core Responsibilities:
- Logging and verbose output management
- Caching system
- Path normalization utilities
- File I/O utilities

#]=======================================================================]

# ==============================================================================
# Logging and Message Functions
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_verbose_message

  Output message only if verbose mode is enabled.

  .. code-block:: cmake

    modula_verbose_message(<message> [MODULE <module>])

  ``message``
    Message to output
  ``MODULE``
    Optional module name for prefixing

  Automatically checks the verbose configuration and only outputs
  the message if verbose mode is enabled.
#]=======================================================================]
function(modula_verbose_message message)
    set(options "")
    set(one_value_args MODULE)
    set(multi_value_args "")
    cmake_parse_arguments(VERBOSE "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})

    # Check if verbose output is enabled
    modula_get_config(MODULA_ENABLE_VERBOSE_OUTPUT verbose_enabled)
    if(verbose_enabled)
        if(VERBOSE_MODULE)
            message(STATUS "[Modula:${VERBOSE_MODULE}] ${message}")
        else()
            message(STATUS "[Modula] ${message}")
        endif()
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_message

  Unified logging function with level support.

  .. code-block:: cmake

    modula_message(<level> <message> [MODULE <module>])

  ``level``
    Message level (STATUS, WARNING, ERROR, FATAL_ERROR)
  ``message``
    Message to output
  ``MODULE``
    Optional module name for prefixing

  Provides unified logging with automatic verbose checking for
  STATUS level messages.
#]=======================================================================]
function(modula_message level message)
    set(options "")
    set(one_value_args MODULE)
    set(multi_value_args "")
    cmake_parse_arguments(LOG "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})

    # Format message with module prefix if provided
    if(LOG_MODULE)
        set(formatted_message "[Modula:${LOG_MODULE}] ${message}")
    else()
        set(formatted_message "[Modula] ${message}")
    endif()

    # Handle different message levels
    if(level STREQUAL "STATUS")
        # For STATUS messages, check verbose setting
        modula_get_config(MODULA_ENABLE_VERBOSE_OUTPUT verbose_enabled)
        if(verbose_enabled)
              message(STATUS "${formatted_message}")
        endif()
    elseif(level STREQUAL "WARNING")
        message(WARNING "${formatted_message}")
    elseif(level STREQUAL "ERROR")
        message(SEND_ERROR "${formatted_message}")
    elseif(level STREQUAL "FATAL_ERROR")
        message(FATAL_ERROR "${formatted_message}")
    else()
        message("${formatted_message}")
    endif()
endfunction()

# ==============================================================================
# Path Utilities
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_normalize_path

  Normalize file system paths.

  .. code-block:: cmake

    modula_normalize_path(<path> <output_var> [BASE_DIR <base>])

  ``path``
    Path to normalize
  ``output_var``
    Variable name to store the normalized path
  ``BASE_DIR``
    Base directory for relative path resolution

  Normalizes file system paths by resolving relative components
  and ensuring consistent path format across platforms.
#]=======================================================================]
function(modula_normalize_path path output_var)
    set(options "")
    set(one_value_args BASE_DIR)
    set(multi_value_args "")
    cmake_parse_arguments(NORM "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})

    # Fast path for already normalized absolute paths
    if(IS_ABSOLUTE "${path}" AND NOT path MATCHES "\\.\\.|//|\\\\\\\\")
        set(${output_var} "${path}" PARENT_SCOPE)
        return()
    endif()

    # Normalize the path
    if(IS_ABSOLUTE "${path}")
        get_filename_component(normalized_path "${path}" ABSOLUTE)
    else()
        if(NORM_BASE_DIR)
            get_filename_component(normalized_path "${NORM_BASE_DIR}/${path}" ABSOLUTE)
        else()
            get_filename_component(normalized_path "${path}" ABSOLUTE)
        endif()
    endif()

    set(${output_var} "${normalized_path}" PARENT_SCOPE)
endfunction()

# ==============================================================================
# Cache System
# ==============================================================================

#[=======================================================================[.rst:
Modula Cache System
==================

**缓存类型：**

1. **memory** (默认)
   - 使用 CMake 内部缓存变量
   - 仅在当前 CMake 运行期间有效
   - 性能最佳，适用于临时数据和配置缓存

2. **file**
   - 使用文件系统存储缓存
   - 跨 CMake 运行持久化
   - 适用于需要持久化的数据，如分析结果和发现结果

**使用示例：**

.. code-block:: cmake

  # 基本用法（内存缓存）
  modula_cache_set("my_key" "my_value" NAMESPACE "my_namespace")
  modula_cache_get("my_key" result NAMESPACE "my_namespace")

  # 文件缓存
  modula_cache_set("persistent_key" "value" NAMESPACE "config" CACHE_TYPE "file")
  modula_cache_get("persistent_key" result NAMESPACE "config" CACHE_TYPE "file")

#]=======================================================================]

function(_modula_cache_init)
    if(NOT DEFINED _MODULA_CACHE_SYSTEM_INITIALIZED)
        set(_MODULA_FILE_CACHE_DIR "${CMAKE_BINARY_DIR}/.modula_cache" CACHE INTERNAL "File cache directory")
        if(NOT EXISTS "${_MODULA_FILE_CACHE_DIR}")
            file(MAKE_DIRECTORY "${_MODULA_FILE_CACHE_DIR}")
        endif()

        modula_get_config(MODULA_ENABLE_INCREMENTAL_BUILD cache_enabled)
        set(_MODULA_CACHE_ENABLED "${cache_enabled}" CACHE INTERNAL "Cache enabled flag")
        set(_MODULA_CACHE_SYSTEM_INITIALIZED TRUE CACHE INTERNAL "Cache system initialized")

        modula_verbose_message("Cache system initialized" MODULE "CacheSystem")
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_cache_get

  Get a value from the cache.

  .. code-block:: cmake

    modula_cache_get(<key> <output_var> [NAMESPACE <namespace>] [CACHE_TYPE <type>])

  ``key``
    Cache key
  ``output_var``
    Variable to store the cached value
  ``NAMESPACE``
    缓存命名空间（可选）
  ``CACHE_TYPE``
    缓存类型：memory（默认）或 file

  **缓存类型说明：**
  - memory: 仅使用内存缓存（默认）
  - file: 仅使用文件缓存

#]=======================================================================]
function(modula_cache_get key output_var)
    _modula_cache_init()

    set(options "")
    set(oneValueArgs NAMESPACE CACHE_TYPE)
    set(multiValueArgs "")
    cmake_parse_arguments(CACHE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    if(NOT CACHE_CACHE_TYPE)
        set(CACHE_CACHE_TYPE "memory")
    endif()

    if(NOT CACHE_CACHE_TYPE MATCHES "^(memory|file)$")
        modula_log_message(WARNING "Invalid CACHE_TYPE '${CACHE_CACHE_TYPE}', using 'memory' as default." MODULE "CacheSystem")
        set(CACHE_CACHE_TYPE "memory")
    endif()

    # 处理不同的缓存类型
    if(CACHE_CACHE_TYPE STREQUAL "memory")
        # 内存缓存实现 - 生成缓存键
        if(CACHE_NAMESPACE)
            set(cache_key "_MODULA_CACHE_${CACHE_NAMESPACE}_${key}")
        else()
            set(cache_key "_MODULA_CACHE_${key}")
        endif()

        if(DEFINED ${cache_key})
            set(${output_var} "${${cache_key}}" PARENT_SCOPE)
        else()
            set(${output_var} "" PARENT_SCOPE)
        endif()
    else()
        # 文件缓存实现 - 生成缓存文件路径
        get_property(cache_dir CACHE _MODULA_FILE_CACHE_DIR PROPERTY VALUE)

        if(CACHE_NAMESPACE)
            set(cache_file "${cache_dir}/${CACHE_NAMESPACE}_${key}.cache")
        else()
            set(cache_file "${cache_dir}/global_${key}.cache")
        endif()

        if(EXISTS "${cache_file}")
            file(READ "${cache_file}" cached_content)
            set(${output_var} "${cached_content}" PARENT_SCOPE)
        else()
            set(${output_var} "" PARENT_SCOPE)
        endif()
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_cache_set

  Set a value in the cache.

  .. code-block:: cmake

    modula_cache_set(<key> <value> [NAMESPACE <namespace>] [CACHE_TYPE <type>])

  ``key``
    Cache key
  ``value``
    Value to cache
  ``NAMESPACE``
    Optional namespace for the cache key
  ``CACHE_TYPE``
    缓存类型：memory（默认）或 file

  **缓存类型说明：**
  - memory: 仅使用内存缓存（默认）
  - file: 仅使用文件缓存

#]=======================================================================]
function(modula_cache_set key value)
    _modula_cache_init()

    set(options "")
    set(oneValueArgs NAMESPACE CACHE_TYPE)
    set(multiValueArgs "")
    cmake_parse_arguments(CACHE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    if(NOT CACHE_CACHE_TYPE)
        set(CACHE_CACHE_TYPE "memory")
    endif()

    if(NOT CACHE_CACHE_TYPE MATCHES "^(memory|file)$")
        modula_log_message(WARNING "Invalid CACHE_TYPE '${CACHE_CACHE_TYPE}', using 'memory' as default." MODULE "CacheSystem")
        set(CACHE_CACHE_TYPE "memory")
    endif()

    if(CACHE_CACHE_TYPE STREQUAL "memory")
        # 内存缓存实现 - 生成缓存键和描述
        if(CACHE_NAMESPACE)
            set(cache_key "_MODULA_CACHE_${CACHE_NAMESPACE}_${key}")
            set(description "Modula cache entry: ${CACHE_NAMESPACE}:${key}")
        else()
            set(cache_key "_MODULA_CACHE_${key}")
            set(description "Modula cache entry: ${key}")
        endif()

        set(${cache_key} "${value}" CACHE INTERNAL "${description}")
    else()
        # 文件缓存实现 - 生成缓存文件路径
        get_property(cache_dir CACHE _MODULA_FILE_CACHE_DIR PROPERTY VALUE)

        if(CACHE_NAMESPACE)
            set(cache_file "${cache_dir}/${CACHE_NAMESPACE}_${key}.cache")
        else()
            set(cache_file "${cache_dir}/global_${key}.cache")
        endif()

        file(WRITE "${cache_file}" "${value}")
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_cache_clear

  Clear cache entries.

  .. code-block:: cmake

    modula_cache_clear([NAMESPACE <namespace>] [CACHE_TYPE <type>])

  ``NAMESPACE``
    要清除的缓存命名空间（可选）
  ``CACHE_TYPE``
    缓存类型：all（默认）、memory 或 file

  **缓存类型说明：**
  - all: 清除所有缓存（默认行为）
  - memory: 仅清除内存缓存，保留文件缓存
  - file: 仅清除文件缓存，保留内存缓存

#]=======================================================================]
function(modula_cache_clear)
    _modula_cache_init()

    set(options "")
    set(oneValueArgs NAMESPACE CACHE_TYPE)
    set(multiValueArgs "")
    cmake_parse_arguments(CACHE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    if(NOT CACHE_CACHE_TYPE)
        set(CACHE_CACHE_TYPE "all")
    endif()

    if(NOT CACHE_CACHE_TYPE MATCHES "^(all|memory|file)$")
        modula_log_message(WARNING "Invalid CACHE_TYPE '${CACHE_CACHE_TYPE}', using 'all' as default." MODULE "CacheSystem")
        set(CACHE_CACHE_TYPE "all")
    endif()

    # 执行相应的清理操作
    if(CACHE_CACHE_TYPE STREQUAL "all")
        # 清理内存缓存
        get_cmake_property(cache_vars CACHE_VARIABLES)
        if(CACHE_NAMESPACE)
            foreach(var ${cache_vars})
                if(var MATCHES "^_MODULA_CACHE_${CACHE_NAMESPACE}_")
                    unset(${var} CACHE)
                endif()
            endforeach()
        else()
            foreach(var ${cache_vars})
                if(var MATCHES "^_MODULA_CACHE_")
                    unset(${var} CACHE)
                endif()
            endforeach()
        endif()

        # 清理文件缓存
        get_property(cache_dir CACHE _MODULA_FILE_CACHE_DIR PROPERTY VALUE)
        if(EXISTS "${cache_dir}")
            if(CACHE_NAMESPACE)
                file(GLOB cache_files "${cache_dir}/${CACHE_NAMESPACE}_*.cache")
            else()
                file(GLOB cache_files "${cache_dir}/*.cache")
            endif()

            foreach(cache_file ${cache_files})
                file(REMOVE "${cache_file}")
            endforeach()
        endif()

        if(CACHE_NAMESPACE)
            modula_verbose_message("Cleared all caches for namespace: ${CACHE_NAMESPACE}" MODULE "CacheSystem")
        else()
            modula_verbose_message("Cleared all caches" MODULE "CacheSystem")
        endif()
    elseif(CACHE_CACHE_TYPE STREQUAL "memory")
        # 清理内存缓存
        get_cmake_property(cache_vars CACHE_VARIABLES)
        if(CACHE_NAMESPACE)
            foreach(var ${cache_vars})
                if(var MATCHES "^_MODULA_CACHE_${CACHE_NAMESPACE}_")
                    unset(${var} CACHE)
                endif()
            endforeach()
        else()
            foreach(var ${cache_vars})
                if(var MATCHES "^_MODULA_CACHE_")
                    unset(${var} CACHE)
                endif()
            endforeach()
        endif()

        if(CACHE_NAMESPACE)
            modula_verbose_message("Cleared memory caches for namespace: ${CACHE_NAMESPACE}" MODULE "CacheSystem")
        else()
            modula_verbose_message("Cleared all memory caches" MODULE "CacheSystem")
        endif()
    elseif(CACHE_CACHE_TYPE STREQUAL "file")
        # 清理文件缓存
        get_property(cache_dir CACHE _MODULA_FILE_CACHE_DIR PROPERTY VALUE)
        if(EXISTS "${cache_dir}")
            if(CACHE_NAMESPACE)
                file(GLOB cache_files "${cache_dir}/${CACHE_NAMESPACE}_*.cache")
            else()
                file(GLOB cache_files "${cache_dir}/*.cache")
            endif()

            foreach(cache_file ${cache_files})
                file(REMOVE "${cache_file}")
            endforeach()
        endif()

        if(CACHE_NAMESPACE)
            modula_verbose_message("Cleared file caches for namespace: ${CACHE_NAMESPACE}" MODULE "CacheSystem")
        else()
            modula_verbose_message("Cleared all file caches" MODULE "CacheSystem")
        endif()
    endif()
endfunction()

function(modula_timestamp_cache_check file_path output_var)
    _modula_cache_init()

    if(NOT EXISTS "${file_path}")
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    file(TIMESTAMP "${file_path}" current_time)

    # 生成缓存键
    string(MD5 file_hash "${file_path}")

    modula_cache_get("${file_hash}" cached_time NAMESPACE "timestamp")

    if(NOT cached_time OR current_time STRGREATER cached_time)
        # 文件已更改或首次检查
        modula_cache_set("${file_hash}" "${current_time}" NAMESPACE "timestamp")
        set(${output_var} TRUE PARENT_SCOPE)
    else()
        # 文件未更改
        set(${output_var} FALSE PARENT_SCOPE)
    endif()
endfunction()

message(STATUS "Modula: Essential utility functions loaded")
