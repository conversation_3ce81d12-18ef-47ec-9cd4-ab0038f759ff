/**
 * @file LoggingModule.module.ixx
 * @brief LoggingModule - 日志模块，依赖CacheModule
 */

module;
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <vector>

export module LoggingModule;
import CacheModule;

export class LoggingModule {
public:
    LoggingModule() = default;
    ~LoggingModule() { if (initialized_) shutdown(); }

    bool initialize() {
        if (initialized_) return true;
        std::cout << "[LoggingModule] 开始初始化日志服务..." << std::endl;
        initialization_time_ = std::chrono::steady_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(150));
        initialized_ = true;
        std::cout << "[LoggingModule] 日志服务初始化完成" << std::endl;
        return true;
    }

    void shutdown() {
        if (!initialized_) return;
        std::cout << "[LoggingModule] 关闭日志服务..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(70));
        initialized_ = false;
        std::cout << "[LoggingModule] 日志服务关闭完成" << std::endl;
    }

    bool is_initialized() const noexcept { return initialized_; }
    std::string get_version() const { return "1.0.0"; }
    std::vector<std::string> get_dependencies() const { return {"CacheModule"}; }
    bool supports_hot_reload() const noexcept { return false; }
    auto get_initialization_time() const noexcept { return initialization_time_; }

    void log(const std::string& level, const std::string& message) {
        if (!initialized_) throw std::runtime_error("LoggingModule not initialized");
        std::cout << "[LoggingModule] " << level << ": " << message << std::endl;
    }

    void log_error(const std::string& message) { log("ERROR", message); }
    void log_info(const std::string& message) { log("INFO", message); }

private:
    bool initialized_ = false;
    std::chrono::steady_clock::time_point initialization_time_;
};
