#!/usr/bin/env python3
"""
Modula Framework - 统一模板管理系统

本文件包含所有C++代码生成模板的统一管理，替代原本分散在configuration.py和processing_pipeline.py中的模板定义。

核心功能：
- 统一的C++代码模板定义
- 模板变量管理和验证
- 模板渲染和格式化
- 模板继承和组合机制

设计原则：
- 单一职责：专注于模板管理
- 易于维护：所有模板集中管理
- 类型安全：完整的模板验证
- 现代C++：使用C++20/23特性

版本: 4.0.0 (重构版)
"""

import re
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod


@dataclass
class TemplateContext:
    """模板渲染上下文"""
    variables: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    generation_time: str = field(default_factory=lambda: datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    generator_version: str = "4.0.0"

    def update(self, **kwargs) -> None:
        """更新模板变量"""
        self.variables.update(kwargs)

    def get(self, key: str, default: Any = None) -> Any:
        """获取模板变量"""
        return self.variables.get(key, default)


class TemplateValidator:
    """模板验证器"""

    @staticmethod
    def validate_cpp_syntax(content: str) -> List[str]:
        """验证C++语法基础规则"""
        errors = []

        # 检查基本的括号匹配
        if content.count('{') != content.count('}'):
            errors.append("Unmatched braces in template")

        if content.count('(') != content.count(')'):
            errors.append("Unmatched parentheses in template")

        # 检查基本的C++关键字使用
        if 'export module' in content and not re.search(r'export\s+module\s+[\w.]+\s*;', content):
            errors.append("Invalid export module syntax")

        return errors

    @staticmethod
    def validate_template_variables(template: str, context: TemplateContext) -> List[str]:
        """验证模板变量完整性"""
        errors = []

        # 查找所有模板变量
        variables_in_template = re.findall(r'\$\{(\w+)\}', template)

        # 内置变量列表
        built_in_vars = {'timestamp', 'generation_time', 'generator_version'}

        for var in variables_in_template:
            if var not in context.variables and var not in built_in_vars:
                errors.append(f"Missing template variable: {var}")

        return errors


class TemplateRenderer:
    """增强的模板渲染器"""

    def __init__(self):
        self.logger = logging.getLogger("template_renderer")
        self.validator = TemplateValidator()

    def render(self, template: str, context: TemplateContext, validate: bool = True) -> str:
        """渲染模板"""
        try:
            if validate:
                # 验证模板变量
                var_errors = self.validator.validate_template_variables(template, context)
                if var_errors:
                    raise ValueError(f"Template validation failed: {'; '.join(var_errors)}")

            # 执行变量替换
            result = template

            # 首先替换内置变量
            built_in_vars = {
                'timestamp': context.timestamp,
                'generation_time': context.generation_time,
                'generator_version': context.generator_version
            }

            for key, value in built_in_vars.items():
                placeholder = f"${{{key}}}"
                result = result.replace(placeholder, str(value))

            # 然后替换用户变量
            for key, value in context.variables.items():
                placeholder = f"${{{key}}}"
                result = result.replace(placeholder, str(value))

            if validate:
                # 验证生成的C++代码基础语法
                cpp_errors = self.validator.validate_cpp_syntax(result)
                if cpp_errors:
                    self.logger.warning(f"Generated C++ may have syntax issues: {'; '.join(cpp_errors)}")

            return result

        except Exception as e:
            self.logger.error(f"Template rendering failed: {e}")
            raise ValueError(f"Template rendering failed: {e}")

    @staticmethod
    def escape_cpp_string(s: str) -> str:
        """转义C++字符串"""
        return s.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')

    @staticmethod
    def format_cpp_array(items: List[str], indent: str = "    ") -> str:
        """格式化C++数组内容"""
        if not items:
            return ""

        escaped_items = [f'{indent}"{TemplateRenderer.escape_cpp_string(item)}"' for item in items]
        return ',\n'.join(escaped_items)


class BaseTemplate(ABC):
    """模板基类"""

    def __init__(self, name: str):
        self.name = name
        self.renderer = TemplateRenderer()
        self.logger = logging.getLogger(f"template.{name}")

    @abstractmethod
    def get_template_content(self) -> str:
        """获取模板内容"""
        pass

    def render(self, context: TemplateContext, validate: bool = True) -> str:
        """渲染模板"""
        template_content = self.get_template_content()
        return self.renderer.render(template_content, context, validate)

    def validate(self, context: TemplateContext) -> List[str]:
        """验证模板"""
        template_content = self.get_template_content()
        errors = []

        # 验证模板变量
        var_errors = self.renderer.validator.validate_template_variables(template_content, context)
        errors.extend(var_errors)

        # 验证C++语法
        cpp_errors = self.renderer.validator.validate_cpp_syntax(template_content)
        errors.extend(cpp_errors)

        return errors


class ModuleTemplate(BaseTemplate):
    """模块的.gen.cpp文件模板"""

    def __init__(self):
        super().__init__("module")

    def get_template_content(self) -> str:
        """获取模块的.gen.cpp模板内容"""
        return '''/**
 * @file ${module_name}.gen.cpp
 * @brief Module metadata implementation for ${module_name}
 *
 * This file provides the concrete metadata for the ${module_name} module.
 * It defines a specialization of the `get_generated_metadata` function template,
 * which is the sole source of truth for this module's metadata.
 *
 * This file is linked into the final executable when the module is used,
 * allowing the framework to access its metadata at runtime without static constructors.
 *
 * Generated at: ${timestamp}
 * Generator version: ${generator_version}
 *
 * DO NOT EDIT MANUALLY - MODIFICATIONS WILL BE OVERWRITTEN
 */

#include <string_view>
#include <array>
#include <span>

import modula.metadata;

// Import the module's interface to get the full type definition for the template specialization.
${module_import_statement}

// ============================================================================
// 模块元数据实现
// ============================================================================

namespace modula::metadata {

namespace detail {
    /**
     * @brief ${module_name}的静态元数据常量
     */
    inline constexpr std::string_view module_name = "${module_name}";
    inline constexpr std::string_view module_target_name = "${module_name}";
    inline constexpr std::string_view module_version = "${version}";
    inline constexpr std::string_view module_directory = "${directory}";
    inline constexpr std::string_view module_primary_file = "${interface_file}";
    inline constexpr std::string_view module_build_timestamp = "${timestamp}";

    // 依赖数组
    inline constexpr std::array<std::string_view, ${dependency_count}> module_dependencies = {
${dependency_names_array}
    };

    // 源文件数组
    inline constexpr std::array<std::string_view, ${source_count}> module_source_files = {
${source_files_array}
    };
}

/**
 * @brief ${module_name}的元数据函数模板特化
 */
/*template<>
inline constexpr modula::Metadata module_metadata<${module_name}>{
    detail::module_name,
    detail::module_target_name,
    detail::module_version,
    detail::module_directory,
    detail::module_primary_file,
    detail::module_build_timestamp,
    std::span<const std::string_view>{detail::module_dependencies},
    std::span<const std::string_view>{detail::module_source_files},
    0,
    1
};*/

// ============================================================================
// 运行时元数据定义（原gen.cpp功能）
// ============================================================================

// 编译时元数据详细定义
namespace detail {
    // 依赖数组（运行时访问）
    inline constexpr std::array<std::string_view, ${dependency_count}> runtime_module_dependencies = {
${dependency_names_array}
    };

    // 源文件数组（运行时访问）
    inline constexpr std::array<std::string_view, ${source_count}> runtime_module_source_files = {
${source_files_array}
    };
}

} // namespace modula::metadata

// ============================================================================
// 元数据注册机制实现
// ============================================================================

namespace {

/**
 * @brief ${module_name}模块的静态元数据定义
 */
static constexpr std::array<std::string_view, ${dependency_count}> dependencies_array{{
${dependency_names_array}
}};

static constexpr std::array<std::string_view, ${source_count}> source_files_array{{
${source_files_array}
}};

static constexpr modula::Metadata ${module_name}_metadata {
    /* name */            "${module_name}",
    /* target_name */     "${module_target_name}",
    /* version */         "${version}",
    /* directory */       "${directory}",
    /* primary_file */    "${interface_file}",
    /* build_timestamp */ "${generation_time}",
    /* dependencies */    std::span<const std::string_view>{dependencies_array},
    /* source_files */    std::span<const std::string_view>{source_files_array},
    /* parallel_group */  ${parallel_group},
    /* priority */        ${priority}
};

/**
 * @brief ${module_name}模块的自动注册器
 *
 * 这个类在静态初始化时自动注册模块元数据，确保在全归档链接时
 * 所有生成的元数据都能被正确注册到系统中。
 *
 * 注册时机：程序启动时，在main函数执行之前
 * 注册方式：调用MetadataRegistry::register_metadata()主动注册
 * 线程安全：静态初始化保证单次执行
 */
class ${module_name}AutoRegistrar {
public:
    ${module_name}AutoRegistrar() {
        // 使用MetadataRegistry进行主动注册
        // 这确保了在modula::register_module<T>()被调用之前，元数据就已经可用
        bool success = modula::MetadataRegistry<${module_class_name}>::register_metadata(${module_name}_metadata);

        // 在调试模式下输出注册状态（可选）
        #ifdef _DEBUG
        if (success) {
            // 注册成功，元数据现在可通过MetadataRegistry访问
        } else {
            // 元数据已经注册过了，这是正常情况
        }
        #endif
    }
};

// 静态实例，确保在程序启动时自动注册
// 这个全局对象的构造函数会在main()函数之前执行
static ${module_name}AutoRegistrar ${module_name}_auto_registrar;

} // anonymous namespace

'''


class GlobalTemplate(BaseTemplate):
    """全局生成文件模板（modula.generated.ixx）"""

    def __init__(self):
        super().__init__("global")

    def get_template_content(self) -> str:
        """获取全局模板内容"""
        return '''/**
 * @file modula.generated.ixx
 * @brief Global compile-time metadata registry and dependency information
 *
 * Generated at: ${timestamp}
 * Generator version: ${generator_version}
 *
 * This file was generated by a script and any modifications will be overwritten.
 * DO NOT EDIT MANUALLY
 *
 * This file contains only compile-time metadata and type information.
 * Actual initialization logic is implemented in ModuleManager to avoid circular dependencies.
 */

module;

#include <string_view>
#include <array>
#include <span>

export module modula.generated;

import modula.types;
import modula.metadata;
${module_imports}

export namespace modula::generated {

// ============================================================================
// 基础模块信息
// ============================================================================

/**
 * @brief Total number of registered modules
 */
inline constexpr std::size_t total_modules = ${total_modules};

/**
 * @brief Array of all module names
 */
inline constexpr std::array<std::string_view, ${total_modules}> module_names = {
${module_names_array}
};

/**
 * @brief 模块前向声明（避免循环依赖）
 */
${conditional_module_forward_declarations}

// ============================================================================
// 编译期类型化功能（基于现代C++20/23）
// ============================================================================

${conditional_typed_features}

// ============================================================================
// 模块信息输出和调试功能
// ============================================================================

/**
 * @brief 获取模块信息摘要
 * @return 包含所有模块基本信息的字符串视图数组
 */
consteval auto get_module_summary() noexcept {
    return module_names;
}

/**
 * @brief 获取依赖关系摘要
 * @return 依赖关系的编译期描述
 */
consteval std::string_view get_dependency_summary() noexcept {
    return "Generated dependency information available at compile time";
}

/**
 * @brief 编译期模块统计信息
 */
struct module_statistics {
    static constexpr std::size_t total_count = total_modules;
    static constexpr std::size_t max_dependency_depth = ${max_dependency_depth};
    static constexpr bool has_parallel_groups = ${has_parallel_groups};
    static constexpr std::string_view generation_time = "${generation_time}";
};

// ============================================================================
// 模块元数据访问接口
// ============================================================================

/**
 * @brief 获取所有模块的名称列表
 * @return 模块名称的编译期数组
 */
consteval auto get_all_module_names() noexcept {
    return module_names;
}

/**
 * @brief 检查模块是否存在
 * @param module_name 模块名称
 * @return 如果模块存在返回true
 */
consteval bool module_exists(std::string_view module_name) noexcept {
    for (const auto& name : module_names) {
        if (name == module_name) {
            return true;
        }
    }
    return false;
}

/**
 * @brief 获取模块索引
 * @param module_name 模块名称
 * @return 模块索引，如果不存在返回SIZE_MAX
 */
consteval std::size_t get_module_index(std::string_view module_name) noexcept {
    for (std::size_t i = 0; i < module_names.size(); ++i) {
        if (module_names[i] == module_name) {
            return i;
        }
    }
    return SIZE_MAX;
}

} // namespace modula::generated'''


class TemplateManager:
    """统一的模板管理器"""

    def __init__(self):
        self.logger = logging.getLogger("template_manager")
        self.renderer = TemplateRenderer()

        # 初始化所有模板
        self.module_template = ModuleTemplate()
        self.global_template = GlobalTemplate()

        # 模板注册表
        self.templates = {
            'module': self.module_template,
            'global': self.global_template
        }

    def get_template(self, template_name: str) -> Optional[BaseTemplate]:
        """获取指定的模板"""
        return self.templates.get(template_name)

    def render_template(self, template_name: str, context: TemplateContext, validate: bool = True) -> str:
        """渲染指定的模板"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")

        return template.render(context, validate)

    def validate_template(self, template_name: str, context: TemplateContext) -> List[str]:
        """验证指定的模板"""
        template = self.get_template(template_name)
        if not template:
            return [f"Template '{template_name}' not found"]

        return template.validate(context)

    def list_templates(self) -> List[str]:
        """列出所有可用的模板"""
        return list(self.templates.keys())

    def validate_all_templates(self, context: TemplateContext) -> Dict[str, List[str]]:
        """验证所有模板"""
        results = {}
        for name, template in self.templates.items():
            results[name] = template.validate(context)
        return results


# ============================================================================
# 便利函数和工厂方法
# ============================================================================

def create_template_context(**kwargs) -> TemplateContext:
    """创建模板上下文的便利函数"""
    context = TemplateContext()
    context.update(**kwargs)
    return context


def create_template_manager() -> TemplateManager:
    """创建模板管理器的工厂函数"""
    return TemplateManager()


def render_module_template(module_data: Dict[str, Any]) -> str:
    """渲染模块模板的便利函数"""
    manager = create_template_manager()
    context = create_template_context(**module_data)
    return manager.render_template('module', context)


def render_global_template(global_data: Dict[str, Any]) -> str:
    """渲染全局模板的便利函数"""
    manager = create_template_manager()
    context = create_template_context(**global_data)
    return manager.render_template('global', context)


# ============================================================================
# 向后兼容性支持
# ============================================================================

class LegacyTemplateAdapter:
    """为了向后兼容而提供的适配器"""

    def __init__(self):
        self.manager = create_template_manager()
        self.renderer = TemplateRenderer()

    def render_template(self, template: str, variables: Dict[str, Any]) -> str:
        """兼容原有的模板渲染接口"""
        context = create_template_context(**variables)
        return self.renderer.render(template, context)

    @staticmethod
    def escape_cpp_string(s: str) -> str:
        """兼容原有的字符串转义函数"""
        return TemplateRenderer.escape_cpp_string(s)
