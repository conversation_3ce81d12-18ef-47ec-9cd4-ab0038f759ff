/**
 * @file modula.types.ixx
 * @brief 基础类型定义
 *
 * 提供模块化框架的核心类型定义：
 * 1. 模块信息结构体和扩展元数据
 * 2. 模块状态枚举和生命周期定义
 * 3. 模块基础接口IModule
 * 4. 异常类型和错误处理
 * 5. 实用工具类型和类型萃取
 * 6. 框架配置和常量定义
 *
 * 这是框架的基础层，被所有其他模块导入使用
 *
 * @version 1.0.0
 */

module;

#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <string_view>
#include <typeinfo>
#include <vector>

export module modula.types;

export namespace modula {

// ============================================================================
// 类型列表基础设施 - 编译时类型操作
// ============================================================================

/**
 * @brief 编译时类型列表模板
 *
 * 提供编译时类型集合操作，支持类型查找、遍历、索引等功能。
 * 这是构建时类型化依赖分析系统的核心基础设施。
 *
 * @tparam Types... 类型参数包
 *
 * @example
 * ```cpp
 * using modules = type_list<ModuleA, ModuleB, ModuleC>;
 * static_assert(modules::size == 3);
 * static_assert(modules::contains<ModuleA>);
 * ```
 */
template<typename... Types>
struct type_list {
    /// 类型列表中的类型数量
    static constexpr std::size_t size = sizeof...(Types);

    /**
     * @brief 检查类型列表是否包含指定类型
     * @tparam T 要检查的类型
     */
    template<typename T>
    static constexpr bool contains = (std::is_same_v<T, Types> || ...);

    /**
     * @brief 获取指定索引位置的类型
     * @tparam Index 类型索引（从0开始）
     */
    template<std::size_t Index>
    using at = std::tuple_element_t<Index, std::tuple<Types...>>;

    /**
     * @brief 对类型列表中的每个类型执行函数
     * @tparam F 函数对象类型
     * @param func 要执行的函数对象
     */
    template<typename F>
    static constexpr void for_each(F&& func) {
        (func.template operator()<Types>(), ...);
    }

    /**
     * @brief 查找类型在列表中的索引
     * @tparam T 要查找的类型
     * @return 类型索引，如果未找到返回size
     */
    template<typename T>
    static constexpr std::size_t index_of() {
        std::size_t index = 0;
        bool found = false;
        ((std::is_same_v<T, Types> ? (found = true) : (found ? false : ++index)), ...);
        return found ? index : size;
    }

    /**
     * @brief 检查类型列表是否为空
     */
    static constexpr bool empty() noexcept {
        return size == 0;
    }
};

/**
 * @brief 序列初始化器模板
 *
 * 提供类型列表的顺序初始化和逆序关闭功能。
 * 支持编译时展开和运行时批量操作。
 *
 * @tparam TypeList 类型列表类型
 *
 * @example
 * ```cpp
 * using init_order = type_list<ModuleA, ModuleB, ModuleC>;
 * sequence_initializer<init_order>::initialize(manager);
 * sequence_initializer<init_order>::shutdown(manager);
 * ```
 */
template<typename TypeList>
struct sequence_initializer;

template<typename... Types>
struct sequence_initializer<type_list<Types...>> {
    /**
     * @brief 按顺序初始化所有类型
     * @tparam ManagerType 管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块初始化成功返回true
     */
    template<typename ManagerType>
    static constexpr bool initialize(ManagerType& manager) {
        return (manager.template initialize_module<Types>() && ...);
    }

    /**
     * @brief 按逆序关闭所有类型
     * @tparam ManagerType 管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块关闭成功返回true
     */
    template<typename ManagerType>
    static constexpr bool shutdown(ManagerType& manager) {
        return shutdown_reverse<Types...>(manager);
    }

    /**
     * @brief 检查所有类型是否已初始化
     * @tparam ManagerType 管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块都已初始化返回true
     */
    template<typename ManagerType>
    static constexpr bool all_initialized(const ManagerType& manager) {
        return (manager.template is_module_initialized<Types>() && ...);
    }

private:
    /**
     * @brief 递归逆序关闭实现
     */
    template<typename First, typename... Rest, typename ManagerType>
    static constexpr bool shutdown_reverse(ManagerType& manager) {
        if constexpr (sizeof...(Rest) > 0) {
            return shutdown_reverse<Rest...>(manager) &&
                   manager.template shutdown_module<First>();
        } else {
            return manager.template shutdown_module<First>();
        }
    }
};

// ============================================================================
// 编译时类型操作工具
// ============================================================================

/**
 * @brief 类型索引映射工具
 *
 * 提供类型到索引的编译时映射功能。
 */
namespace type_utils {

/**
 * @brief 获取类型在类型列表中的索引
 * @tparam T 目标类型
 * @tparam TypeList 类型列表
 */
template<typename T, typename TypeList>
constexpr std::size_t get_type_index() {
    return TypeList::template index_of<T>();
}

/**
 * @brief 检查类型是否在类型列表中
 * @tparam T 目标类型
 * @tparam TypeList 类型列表
 */
template<typename T, typename TypeList>
constexpr bool contains_type() {
    return TypeList::template contains<T>;
}

/**
 * @brief 类型列表连接工具
 * @tparam List1 第一个类型列表
 * @tparam List2 第二个类型列表
 */
template<typename List1, typename List2>
struct concat;

template<typename... Types1, typename... Types2>
struct concat<type_list<Types1...>, type_list<Types2...>> {
    using type = type_list<Types1..., Types2...>;
};

template<typename List1, typename List2>
using concat_t = typename concat<List1, List2>::type;

} // namespace type_utils

// ============================================================================
// 模块基本概念定义 - 必须在依赖验证之前定义
// ============================================================================

/**
 * @brief 模块基本概念 - 定义模块必须满足的完整接口约束
 *
 * 符合README.md中条件3的概念约束：主模块文件必须实现符合概念约束的生命周期管理类
 * 这是ModulaFramework的核心概念，定义了模块的基本契约和类型安全保证
 *
 * 模块必须满足以下要求：
 * 1. 具有完整的生命周期管理接口
 * 2. 支持元数据特征提取
 * 3. 满足基本的C++类型要求
 * 4. 提供状态查询能力
 */
template<typename T>
concept Module = requires(T& t) {
    // 生命周期管理接口
    { t.initialize() } -> std::same_as<bool>;     ///< 初始化模块，返回成功状态
    { t.shutdown() } -> std::same_as<void>;       ///< 关闭模块，无返回值
    { t.is_initialized() } -> std::same_as<bool>; ///< 检查初始化状态
} && requires(const T& ct) {
    // 常量成员函数要求
    { ct.is_initialized() } -> std::same_as<bool>; ///< 状态查询必须是const成员函数
} && requires {
    // 编译期类型检查 - 确保类型安全
    std::is_class_v<T>;                 ///< 模块必须是类类型
    std::is_default_constructible_v<T>; ///< 模块必须可默认构造
    std::is_destructible_v<T>;          ///< 模块必须可析构
    !std::is_abstract_v<T>;             ///< 模块不能是抽象类
};

// ============================================================================
// 编译时依赖验证系统
// ============================================================================

/**
 * @brief 编译时循环依赖检测工具
 */
namespace dependency_validation {

/**
 * @brief 检查类型是否在类型列表中
 * @tparam T 目标类型
 * @tparam TypeList 类型列表
 */
template<typename T, typename TypeList>
struct contains_type;

template<typename T, typename... Types>
struct contains_type<T, type_list<Types...>> {
    static constexpr bool value = (std::is_same_v<T, Types> || ...);
};

template<typename T, typename TypeList>
constexpr bool contains_type_v = contains_type<T, TypeList>::value;

/**
 * @brief 编译时循环依赖检测
 * @tparam T 当前检查的类型
 * @tparam DepList 依赖类型列表
 * @tparam Visited 已访问的类型列表（用于检测循环）
 */
template<typename T, typename DepList, typename Visited = type_list<>>
struct has_circular_dependency {
    static constexpr bool value = false;
};

/**
 * @brief 循环依赖检测的递归实现
 */
template<typename T, typename... Deps, typename... VisitedTypes>
struct has_circular_dependency<T, type_list<Deps...>, type_list<VisitedTypes...>> {
private:
    // 检查T是否已在访问路径中
    static constexpr bool self_cycle = contains_type_v<T, type_list<VisitedTypes...>>;

    // 将T添加到访问路径
    using new_visited = type_list<VisitedTypes..., T>;

    // 递归检查每个依赖
    template<typename Dep>
    static constexpr bool check_dependency() {
        if constexpr (contains_type_v<Dep, new_visited>) {
            return true; // 发现循环
        } else {
            // 这里需要获取Dep的依赖列表，暂时返回false
            // 在实际使用中，这会通过模板特化来提供依赖信息
            return false;
        }
    }

public:
    static constexpr bool value = self_cycle || (check_dependency<Deps>() || ...);
};

template<typename T, typename DepList, typename Visited = type_list<>>
constexpr bool has_circular_dependency_v = has_circular_dependency<T, DepList, Visited>::value;

/**
 * @brief 计算依赖深度
 * @tparam T 目标类型
 * @tparam DepList 依赖类型列表
 */
template<typename T, typename DepList>
struct dependency_depth {
    static constexpr std::size_t value = 0;
};

template<typename T, typename... Deps>
struct dependency_depth<T, type_list<Deps...>> {
private:
    template<typename Dep>
    static constexpr std::size_t get_dep_depth() {
        // 这里需要递归获取依赖的深度
        // 在实际使用中，这会通过模板特化来提供
        return 1;
    }

    static constexpr std::size_t max_dep_depth() {
        if constexpr (sizeof...(Deps) == 0) {
            return 0;
        } else {
            std::size_t max_depth = 0;
            ((max_depth = std::max(max_depth, get_dep_depth<Deps>())), ...);
            return max_depth;
        }
    }

public:
    static constexpr std::size_t value = max_dep_depth() + 1;
};

template<typename T, typename DepList>
constexpr std::size_t dependency_depth_v = dependency_depth<T, DepList>::value;

/**
 * @brief 计算类型列表中所有类型的最大依赖深度
 * @tparam TypeList 类型列表
 */
template<typename TypeList>
struct max_depth_of;

template<typename... Types>
struct max_depth_of<type_list<Types...>> {
private:
    template<typename T>
    static constexpr std::size_t get_type_depth() {
        // 这里需要获取每个类型的依赖深度
        // 在实际使用中，这会通过模板特化来提供
        return 1;
    }

public:
    static constexpr std::size_t value = []() {
        if constexpr (sizeof...(Types) == 0) {
            return std::size_t{0};
        } else {
            std::size_t max_depth = 0;
            ((max_depth = std::max(max_depth, get_type_depth<Types>())), ...);
            return max_depth;
        }
    }();
};

template<typename TypeList>
constexpr std::size_t max_depth_of_v = max_depth_of<TypeList>::value;

/**
 * @brief 计算模块的依赖深度（便捷函数）
 * @tparam T 模块类型
 */
template<typename T>
consteval std::size_t calculate_dependency_depth() {
    // 这里需要获取T的依赖列表
    // 在实际使用中，这会通过模板特化来提供
    using deps = type_list<>; // 默认无依赖
    return dependency_depth_v<T, deps>;
}

/**
 * @brief 验证依赖关系的有效性
 * @tparam T 模块类型
 * @tparam DepList 依赖类型列表
 */
template<typename T, typename DepList>
consteval bool validate_dependencies() {
    // 检查循环依赖
    if constexpr (has_circular_dependency_v<T, DepList>) {
        return false;
    }

    // 检查依赖深度是否合理（避免过深的依赖链）
    constexpr std::size_t max_allowed_depth = 10;
    if constexpr (dependency_depth_v<T, DepList> > max_allowed_depth) {
        return false;
    }

    return true;
}

/**
 * @brief 高级类型安全检查
 */
namespace type_safety {

/**
 * @brief 检查所有依赖类型是否满足Module概念
 * @tparam DepList 依赖类型列表
 */
template<typename DepList>
struct all_dependencies_are_modules;

template<typename... Deps>
struct all_dependencies_are_modules<type_list<Deps...>> {
    static constexpr bool value = (Module<Deps> && ...);
};

template<typename DepList>
constexpr bool all_dependencies_are_modules_v = all_dependencies_are_modules<DepList>::value;

/**
 * @brief 检查依赖列表中是否有重复类型
 * @tparam DepList 依赖类型列表
 */
template<typename DepList>
struct has_duplicate_dependencies;

template<typename... Deps>
struct has_duplicate_dependencies<type_list<Deps...>> {
private:
    template<typename T, typename... Rest>
    static constexpr bool check_duplicates() {
        if constexpr (sizeof...(Rest) == 0) {
            return false;
        } else {
            return contains_type_v<T, type_list<Rest...>> || check_duplicates<Rest...>();
        }
    }

public:
    static constexpr bool value = check_duplicates<Deps...>();
};

template<typename DepList>
constexpr bool has_duplicate_dependencies_v = has_duplicate_dependencies<DepList>::value;

/**
 * @brief 检查模块是否依赖自己
 * @tparam T 模块类型
 * @tparam DepList 依赖类型列表
 */
template<typename T, typename DepList>
constexpr bool has_self_dependency_v = contains_type_v<T, DepList>;

/**
 * @brief 全面的类型安全验证
 * @tparam T 模块类型
 * @tparam DepList 依赖类型列表
 */
template<typename T, typename DepList>
consteval bool comprehensive_type_safety_check() {
    // 检查模块类型本身是否满足Module概念
    if constexpr (!Module<T>) {
        return false;
    }

    // 检查所有依赖是否满足Module概念
    if constexpr (!all_dependencies_are_modules_v<DepList>) {
        return false;
    }

    // 检查是否有重复依赖
    if constexpr (has_duplicate_dependencies_v<DepList>) {
        return false;
    }

    // 检查是否有自依赖
    if constexpr (has_self_dependency_v<T, DepList>) {
        return false;
    }

    return true;
}

} // namespace type_safety

/**
 * @brief 增强的依赖关系验证（包含类型安全检查）
 * @tparam T 模块类型
 * @tparam DepList 依赖类型列表
 */
template<typename T, typename DepList>
consteval bool validate_dependencies_enhanced() {
    // 基础验证
    if constexpr (!validate_dependencies<T, DepList>()) {
        return false;
    }

    // 类型安全检查
    if constexpr (!type_safety::comprehensive_type_safety_check<T, DepList>()) {
        return false;
    }

    return true;
}

/**
 * @brief 编译时依赖关系验证宏
 *
 * 在编译时验证模块的依赖关系是否有效。
 *
 * @param ModuleType 模块类型
 * @param ... 依赖类型列表
 */
#define VALIDATE_MODULE_DEPENDENCIES(ModuleType, ...) \
    static_assert(modula::dependency_validation::validate_dependencies<ModuleType, modula::type_list<__VA_ARGS__>>(), \
                  "Invalid dependency configuration for module " #ModuleType)

/**
 * @brief 增强的编译时依赖关系验证宏
 *
 * 在编译时验证模块的依赖关系和类型安全性。
 *
 * @param ModuleType 模块类型
 * @param ... 依赖类型列表
 */
#define VALIDATE_MODULE_DEPENDENCIES_ENHANCED(ModuleType, ...) \
    static_assert(modula::dependency_validation::validate_dependencies_enhanced<ModuleType, modula::type_list<__VA_ARGS__>>(), \
                  "Invalid dependency configuration or type safety violation for module " #ModuleType)

} // namespace dependency_validation

/**
 * @brief 模块状态枚举
 *
 * 定义模块在生命周期中的各种状态，符合README.md中的生命周期管理规范
 */
enum class ModuleState {
    Uninitialized, ///< 未初始化状态
    Initializing,  ///< 正在初始化
    Initialized,   ///< 已初始化，可正常使用
    Shutting_down, ///< 正在关闭
    Shutdown,      ///< 已关闭
    Error,         ///< 错误状态
    Suspended,     ///< 挂起状态（用于热重载）
    Failed         ///< 初始化失败
};

/**
 * @brief 模块状态转换为字符串
 *
 * 编译期函数，将模块状态枚举值转换为对应的字符串表示
 *
 * @param state 要转换的模块状态枚举值
 * @return consteval std::string_view 状态的字符串表示，保证编译期计算
 */
consteval std::string_view to_string(ModuleState state) noexcept {
    switch (state) {
    case ModuleState::Uninitialized:
        return "Uninitialized";
    case ModuleState::Initializing:
        return "Initializing";
    case ModuleState::Initialized:
        return "Initialized";
    case ModuleState::Shutting_down:
        return "Shutting_down";
    case ModuleState::Shutdown:
        return "Shutdown";
    case ModuleState::Error:
        return "Error";
    case ModuleState::Suspended:
        return "Suspended";
    case ModuleState::Failed:
        return "Failed";
    default:
        return "Unknown";
    }
}

/**
 * @brief 检查模块状态是否为活跃状态
 *
 * 编译期函数，判断模块是否处于可以正常工作的活跃状态
 *
 * @param state 要检查的模块状态
 * @return consteval bool 如果模块处于活跃状态返回true，否则返回false
 */
consteval bool is_active_state(ModuleState state) noexcept {
    return state == ModuleState::Initialized || state == ModuleState::Suspended;
}

/**
 * @brief 检查模块状态是否为终止状态
 *
 * 编译期函数，判断模块是否处于不可恢复的终止状态
 *
 * @param state 要检查的模块状态
 * @return consteval bool 如果模块处于终止状态返回true，否则返回false
 */
consteval bool is_terminal_state(ModuleState state) noexcept {
    return state == ModuleState::Shutdown || state == ModuleState::Failed || state == ModuleState::Error;
}

/**
 * @brief 模块优先级常量定义
 */
namespace priority {
constexpr int CRITICAL = 100; ///< 关键模块优先级
constexpr int HIGH = 75;      ///< 高优先级
constexpr int NORMAL = 50;    ///< 普通优先级
constexpr int LOW = 25;       ///< 低优先级
constexpr int BACKGROUND = 0; ///< 后台模块优先级
} // namespace priority

/**
 * @brief 框架配置常量
 */
namespace config {
constexpr int DEFAULT_TIMEOUT_MS = 5000;              ///< 默认超时时间（毫秒）
constexpr int DEFAULT_MEMORY_LIMIT_MB = 100;          ///< 默认内存限制（MB）
constexpr int MAX_CONCURRENT_INIT = 4;                ///< 最大并发初始化数量
constexpr std::string_view DEFAULT_VERSION = "1.0.0"; ///< 默认版本号
} // namespace config

/**
 * @brief 框架配置结构
 *
 * 定义整个框架的配置参数
 */
struct Config
{
    // 核心配置
    bool auto_discover_modules = true;         ///< 是否自动发现模块
    bool generate_metadata_files = false;      ///< 是否生成元数据文件
    bool enable_hot_reload = false;            ///< 是否启用热重载
    bool enable_lazy_loading = false;          ///< 是否启用延迟加载
    bool enable_performance_monitoring = true; ///< 是否启用性能监控
    bool enable_parallel_init = false;         ///< 是否启用并行初始化
    bool strict_dependency_check = true;       ///< 是否启用严格依赖检查

    // 路径配置
    std::string module_search_path = ".";           ///< 模块搜索路径
    std::string metadata_output_dir = "./metadata"; ///< 元数据输出目录
    std::string cache_directory = "./cache";        ///< 缓存目录

    // 性能配置
    int default_timeout_ms = config::DEFAULT_TIMEOUT_MS;              ///< 默认超时时间
    int max_concurrent_initializations = config::MAX_CONCURRENT_INIT; ///< 最大并发初始化数量
    int memory_limit_mb = 1024;                                       ///< 内存限制
    int max_init_timeout_ms = config::DEFAULT_TIMEOUT_MS;             ///< 最大初始化超时时间

    // 输出配置
    std::string metadata_format = "json"; ///< 元数据格式
    std::string log_level = "INFO";       ///< 日志级别
    bool verbose_output = false;          ///< 是否详细输出

    /**
     * @brief 验证配置的有效性
     * @return true 如果配置有效
     */
    [[nodiscard]] bool is_valid() const noexcept {
        return default_timeout_ms > 0 && max_concurrent_initializations > 0 && memory_limit_mb > 0;
    }
};

/**
 * @brief 模块异常基类
 *
 * 所有模块相关异常的基类
 */
class ModuleException : public std::exception
{
public:
    explicit ModuleException(std::string message)
        : message_(std::move(message)) {}

    [[nodiscard]] const char* what() const noexcept override {
        return message_.c_str();
    }

    [[nodiscard]] const std::string& get_message() const noexcept {
        return message_;
    }

private:
    std::string message_;
};

/**
 * @brief 元数据加载异常类
 */
class MetadataLoadException : public ModuleException
{
public:
    explicit MetadataLoadException(const std::string& message)
        : ModuleException("Metadata load error: " + message) {}
};

/**
 * @brief 路径宏未定义异常类
 */
class PathMacroUndefinedException : public MetadataLoadException
{
public:
    explicit PathMacroUndefinedException(const std::string& macro_name)
        : MetadataLoadException("Required path macro '" + macro_name + "' is not defined. "
                                                                       "Ensure CMake target_compile_definitions() includes this macro.") {}
};

/**
 * @brief 模块注册异常
 */
class ModuleRegistrationException : public ModuleException
{
public:
    explicit ModuleRegistrationException(const std::string& module_name)
        : ModuleException("Failed to register module: " + module_name)
        , module_name_(module_name) {}

    [[nodiscard]] const std::string& get_module_name() const noexcept {
        return module_name_;
    }

private:
    std::string module_name_;
};

/**
  * @brief 模块管理器异常基类
  *
  * 模块管理器相关操作的异常基类，提供详细的错误信息
  */
class ModuleManagerException : public ModuleException
{
public:
    explicit ModuleManagerException(std::string_view message)
        : ModuleException(std::string(message)) {}
};

/**
 * @brief 模块初始化失败异常
 *
 * 当模块初始化过程中发生错误时抛出的异常
 * 包含失败的模块名称和详细的错误信息
 */
class ModuleInitializationException : public ModuleManagerException
{
public:
    explicit ModuleInitializationException(const std::string& module_name)
        : ModuleManagerException("Failed to initialize module: " + module_name)
        , module_name_(module_name) {}

    explicit ModuleInitializationException(const std::string& module_name, const std::string& reason)
        : ModuleManagerException("Failed to initialize module '" + module_name + "': " + reason)
        , module_name_(module_name) {}

    /**
     * @brief 获取失败的模块名称
     *
     * @return const std::string& 模块名称
     */
    [[nodiscard]] const std::string& get_module_name() const noexcept {
        return module_name_;
    }

private:
    std::string module_name_;
};

/**
 * @brief 模块配置异常
 */
class ModuleConfigurationException : public ModuleManagerException {
public:
    explicit ModuleConfigurationException(const std::string& message)
        : ModuleManagerException("Configuration error: " + message) {}
};

/**
  * @brief 模块管理器循环依赖异常
  *
  * 当模块管理器检测到循环依赖时抛出的异常
  * 包含完整的循环路径信息和详细的错误描述
  */
class ModuleManagerCircularDependencyException : public ModuleManagerException
{
public:
    explicit ModuleManagerCircularDependencyException(const std::vector<std::string>& cycle)
        : ModuleManagerException("Circular dependency detected in module initialization: " + format_cycle(cycle))
        , cycle_(cycle) {}

    /**
      * @brief 获取循环依赖路径
      *
      * @return const std::vector<std::string>& 循环依赖的模块名称列表
      */
    [[nodiscard]] const std::vector<std::string>& get_cycle() const noexcept {
        return cycle_;
    }

    /**
      * @brief 获取循环长度
      *
      * @return size_t 循环中包含的模块数量
      */
    [[nodiscard]] size_t get_cycle_length() const noexcept {
        return cycle_.size();
    }

private:
    std::vector<std::string> cycle_;

    /**
      * @brief 格式化循环依赖路径
      *
      * @param cycle 循环依赖路径
      * @return std::string 格式化的循环路径字符串
      */
    static std::string format_cycle(const std::vector<std::string>& cycle) {
        if (cycle.empty()) {
            return "empty cycle";
        }

        std::string result;
        for (size_t i = 0; i < cycle.size(); ++i) {
            if (i > 0)
                result += " -> ";
            result += cycle[i];
        }
        result += " -> " + cycle[0];
        return result;
    }
};
/**
 * @brief 依赖图异常类
 *
 * 依赖图操作相关的异常基类，提供详细的错误信息
 */
class DependencyGraphException : public ModuleException
{
public:
    explicit DependencyGraphException(std::string_view message)
        : ModuleException(std::string(message)) {}
};

/**
 * @brief 循环依赖异常
 */
class CircularDependencyException : public ModuleException
{
public:
    explicit CircularDependencyException(const std::vector<std::string>& cycle)
        : ModuleException(build_message(cycle))
        , cycle_(cycle) {}

    /**
     * @brief 获取循环依赖路径
     *
     * @return const std::vector<std::string>& 循环依赖的模块名称列表
     */
    [[nodiscard]] const std::vector<std::string>& get_cycle() const noexcept {
        return cycle_;
    }

    /**
     * @brief 获取循环长度
     *
     * @return size_t 循环中包含的模块数量
     */
    [[nodiscard]] size_t get_cycle_length() const noexcept {
        return cycle_.size();
    }

private:
    std::vector<std::string> cycle_;

    /**
     * @brief 构建循环依赖的错误消息
     *
     * @param cycle 循环依赖路径
     * @return std::string 格式化的错误消息
     */
    static std::string build_message(const std::vector<std::string>& cycle) {
        if (cycle.empty()) {
            return "Empty circular dependency detected";
        }

        std::string message = "Circular dependency detected: ";
        for (size_t i = 0; i < cycle.size(); ++i) {
            if (i > 0)
                message += " -> ";
            message += cycle[i];
        }
        message += " -> " + cycle[0];
        return message;
    }
};

/**
 * @brief 模块实例持有者 - 类型擦除的RAII容器
 *
 * 提供类型安全的模块实例管理，支持异常安全的资源管理
 * 使用RAII机制确保资源正确释放，支持移动语义优化性能
 */
class ModuleInstanceHolder
{
public:
    /**
     * @brief 构造函数
     *
     * 从unique_ptr构造实例持有者，转移所有权并设置类型信息
     *
     * @tparam T 模块类型，必须可析构
     * @param instance 模块实例的唯一指针，不能为空
     * @throws std::invalid_argument 如果instance为空
     */
    template<typename T>
    explicit ModuleInstanceHolder(std::unique_ptr<T> instance)
        : deleter_([](void* ptr) noexcept {
            delete static_cast<T*>(ptr);
        })
        , type_info_(&typeid(T)) {
        if (!instance) {
            throw std::invalid_argument("ModuleInstanceHolder: instance cannot be null");
        }
        raw_ptr_ = instance.release();
    }

    /**
     * @brief 析构函数 - RAII资源清理
     *
     * 自动调用删除器释放持有的模块实例
     */
    ~ModuleInstanceHolder() noexcept {
        cleanup();
    }

    // 禁用拷贝语义 - 确保唯一所有权
    ModuleInstanceHolder(const ModuleInstanceHolder&) = delete;
    ModuleInstanceHolder& operator=(const ModuleInstanceHolder&) = delete;

    ModuleInstanceHolder(ModuleInstanceHolder&& other) noexcept
        : raw_ptr_(std::exchange(other.raw_ptr_, nullptr))
        , deleter_(std::exchange(other.deleter_, nullptr))
        , type_info_(std::exchange(other.type_info_, nullptr)) {
    }

    ModuleInstanceHolder& operator=(ModuleInstanceHolder&& other) noexcept {
        if (this != &other) {
            cleanup();
            raw_ptr_ = std::exchange(other.raw_ptr_, nullptr);
            deleter_ = std::exchange(other.deleter_, nullptr);
            type_info_ = std::exchange(other.type_info_, nullptr);
        }
        return *this;
    }

    /**
     * @brief 获取指定类型的模块实例
     *
     * 类型安全的实例访问，通过RTTI验证类型匹配
     *
     * @tparam T 目标模块类型
     * @return T* 类型化的模块指针，如果类型不匹配或实例无效返回nullptr
     */
    template<typename T>
    [[nodiscard]] T* get_as() const noexcept {
        if (type_info_ && *type_info_ == typeid(T) && raw_ptr_) {
            return static_cast<T*>(raw_ptr_);
        }
        return nullptr;
    }

    /**
     * @brief 获取原始指针
     *
     * 直接访问底层指针，使用时需要确保类型安全
     *
     * @return void* 原始void指针，可能为nullptr
     */
    [[nodiscard]] void* get_raw() const noexcept {
        return raw_ptr_;
    }

    /**
     * @brief 检查是否持有有效实例
     *
     * @return bool 如果持有有效实例返回true，否则返回false
     */
    [[nodiscard]] bool is_valid() const noexcept {
        return raw_ptr_ != nullptr && deleter_ != nullptr;
    }

private:
    /**
     * @brief 内部清理函数 - 异常安全的资源释放
     */
    void cleanup() noexcept {
        if (raw_ptr_ && deleter_) {
            try {
                deleter_(raw_ptr_);
            } catch (...) {
                // 析构函数中不能抛出异常，静默处理
            }
        }
        raw_ptr_ = nullptr;
        deleter_ = nullptr;
        type_info_ = nullptr;
    }

    void* raw_ptr_ = nullptr;                   ///< 原始指针
    std::function<void(void*)> deleter_;        ///< 删除器函数（noexcept保证）
    const std::type_info* type_info_ = nullptr; ///< 类型信息
};

/**
 * @brief 模块接口类模板 - CRTP基础模块实现
 *
 * 使用CRTP（Curiously Recurring Template Pattern）提供模块的默认实现
 * 继承此类可以简化模块开发，自动提供生命周期管理和状态跟踪
 * 支持自动化元数据生成和性能监控
 *
 * @tparam Derived 派生类类型，用于CRTP模式
 *
 * @example
 * ```cpp
 * class MyModule : public IModule<MyModule> {
 * protected:
 *     bool do_initialize() override { return true; }
 *     void do_shutdown() override {}
 * };
 * ```
 */
template<typename Derived>
class IModule
{
public:
    virtual ~IModule() = default;

    /**
     * @brief 初始化模块
     *
     * 线程安全的模块初始化，支持重复调用检查和时间统计
     *
     * @return bool 初始化成功返回true，失败返回false
     */
    virtual bool initialize() {
        if (initialized_) {
            return true;
        }

        const bool success = do_initialize();
        if (success) {
            initialized_ = true;
            initialization_time_ = std::chrono::steady_clock::now();
        }
        return success;
    }

    /**
     * @brief 关闭模块
     *
     * 线程安全的模块关闭，支持重复调用检查
     */
    virtual void shutdown() {
        if (!initialized_) {
            return;
        }

        do_shutdown();
        initialized_ = false;
    }

    /**
     * @brief 检查模块是否已初始化
     *
     * @return bool 如果已初始化返回true，否则返回false
     */
    [[nodiscard]] constexpr bool is_initialized() const noexcept {
        return initialized_;
    }

    /**
     * @brief 获取模块初始化时间戳
     *
     * @return std::chrono::steady_clock::time_point 初始化时间点，如果未初始化则返回默认值
     */
    [[nodiscard]] constexpr auto get_initialization_time() const noexcept {
        return initialization_time_;
    }

    /**
     * @brief 获取模块运行时长
     *
     * 计算从初始化到当前时刻的运行时长
     *
     * @return std::chrono::milliseconds 运行时长，如果未初始化则返回零时长
     */
    [[nodiscard]] auto get_uptime() const noexcept {
        if (!initialized_) {
            return std::chrono::milliseconds{0};
        }
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - initialization_time_);
    }

protected:
    /**
     * @brief 子类实现的实际初始化逻辑
     *
     * 派生类应该重写此方法实现具体的初始化逻辑
     *
     * @return bool 初始化成功返回true，失败返回false
     */
    virtual bool do_initialize() { return true; }

    /**
     * @brief 子类实现的实际关闭逻辑
     *
     * 派生类应该重写此方法实现具体的清理逻辑
     */
    virtual void do_shutdown() {}

private:
    bool initialized_ = false;                                  ///< 初始化状态标志
    std::chrono::steady_clock::time_point initialization_time_; ///< 初始化时间戳
};

/**
 * @brief Transparent hash for string types
 *
 * This hash allows heterogeneous lookup in unordered_map with string_view
 * without converting to std::string. It's compatible with C++20's transparent
 * comparator concept.
 */
struct StringViewHash {
    using is_transparent = void; // Mark as transparent

    // Hash functions for different string types
    size_t operator()(const char* str) const {
        return std::hash<std::string_view>{}(str);
    }

    size_t operator()(std::string_view str) const {
        return std::hash<std::string_view>{}(str);
    }

    size_t operator()(const std::string& str) const {
        return std::hash<std::string_view>{}(str);
    }
};

// ============================================================================
// 编译时类型索引机制 - 通用模板定义
// ============================================================================

/**
 * @brief 编译时类型索引特征模板
 * @tparam T 模块类型
 */
template<typename T>
struct type_index_traits {
    static constexpr std::size_t index = SIZE_MAX;  // 默认无效索引
    static constexpr std::size_t type_tag = 0;      // 默认无效标签
    static constexpr bool is_registered = false;    // 默认未注册
};

/**
 * @brief 编译时类型索引获取函数（类似Qt的qMetaTypeId）
 * @tparam T 模块类型
 * @return 编译时类型索引
 */
template<typename T>
consteval std::size_t meta_type_index() noexcept {
    return type_index_traits<T>::index;
}

/**
 * @brief 编译时类型标签获取函数
 * @tparam T 模块类型
 * @return 编译时类型标签
 */
template<typename T>
consteval std::size_t meta_type_tag() noexcept {
    return type_index_traits<T>::type_tag;
}

/**
 * @brief 编译时类型注册状态检查
 * @tparam T 模块类型
 * @return 是否已注册
 */
template<typename T>
consteval bool is_type_registered() noexcept {
    return type_index_traits<T>::is_registered;
}

/**
 * @brief 类型标签到索引的映射结构
 */
struct TypeTagMapping {
    std::size_t type_tag;
    std::size_t index;
    std::string_view name;
};

/**
 * @brief 运行时类型信息结构（避免循环依赖）
 */
struct runtime_type_info {
    std::size_t type_tag;
    std::size_t index;
    std::string_view name;

    constexpr runtime_type_info(std::size_t tag, std::size_t idx, std::string_view n) noexcept
        : type_tag(tag), index(idx), name(n) {}
};

} // namespace modula
