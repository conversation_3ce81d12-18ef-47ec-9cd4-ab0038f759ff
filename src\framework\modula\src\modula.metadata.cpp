/**
 * @file modula.metadata.cpp
 * @brief Modula metadata implementation
 *
 * Implements the runtime metadata loading system.
 *
 * @version 1.0.0
 */

module;

#include <string_view>
#include <array>
#include <type_traits>
#include <concepts>
#include <algorithm>

module modula.metadata;

import modula.types;
import modula.info;
// import modula.generated;

namespace modula::metadata {

// 简化的验证函数实现

template<typename T>
consteval bool is_module_metadata_valid() noexcept {
    // 简化版本：检查是否能获取metadata
    try {
        const auto& metadata = get_metadata<T>();
        return !metadata.name.empty() && !metadata.version.empty();
    } catch (...) {
        return false;
    }
}

template<typename T>
consteval bool is_module_has_metadata() noexcept {
    return is_module_metadata_valid<T>();
}

consteval bool is_system_metadata_valid() noexcept {
    return true;//modula::generated::total_modules > 0;
}

} // namespace modula::metadata
