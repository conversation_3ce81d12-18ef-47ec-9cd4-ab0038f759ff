# TestLib CMakeLists.txt
# Traditional C++ static library for demonstrating interoperability with C++20 modules
# This library provides utility functions that can be used by both traditional C++ code and C++20 modules

# Create TestLib static library
add_library(TestLib STATIC)

# Add source files
target_sources(TestLib
    PRIVATE
        test_lib.cpp
    PUBLIC
        test_lib.h
)

# Set C++ standard
set_target_properties(TestLib PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# Set include directories
target_include_directories(TestLib
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# enable_modula_framework(TestLib)

# ============================================================================
# Enhanced Encoding and Compiler Configuration for TestLib
# ============================================================================

# Apply enhanced encoding settings
if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(TestLib PRIVATE
        /W4                     # High warning level
        /permissive-           # Strict conformance mode
        /utf-8                 # UTF-8 source and execution character sets
        /std:c++latest        # Latest C++ standard
        /bigobj               # Support large object files
        /Zc:__cplusplus       # Correct __cplusplus macro value
        /wd4819               # Disable specific encoding warning
    )
    target_compile_definitions(TestLib PRIVATE
        UNICODE                  # Enable Unicode support
        _UNICODE                 # Enable Unicode support
        _CRT_SECURE_NO_WARNINGS # Disable CRT security warnings
        NOMINMAX                # Prevent min/max macro conflicts
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(TestLib PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
        -fwide-exec-charset=UTF-32LE # Wide character set
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(TestLib PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
    )
endif()

# Add compile definitions
target_compile_definitions(TestLib PRIVATE
    TESTLIB_VERSION="1.0.0"
    TESTLIB_NAME="TestLib"
    TESTLIB_STATIC_LIBRARY=1
)

# Set library output directory
set_target_properties(TestLib PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# Add library description
set_target_properties(TestLib PROPERTIES
    DESCRIPTION "Traditional C++ utility library for mathematical calculations, string processing, and data validation"
    VERSION "1.0.0"
)

# Export the library for use by other targets
# This makes TestLib available to modules and other libraries in the project
set_property(TARGET TestLib PROPERTY EXPORT_NAME TestLib)

# Add installation rules (optional)
install(TARGETS TestLib
    EXPORT TestLibTargets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(FILES test_lib.h
    DESTINATION include/testlib
)

# Configuration notes:
# 1. This is a traditional C++ static library, not a module
# 2. It can be linked to and used by C++20 modules
# 3. The library provides utility functions for:
#    - Mathematical calculations (factorial, gcd, lcm, prime checking, etc.)
#    - String processing (case conversion, trimming, splitting, etc.)
#    - Data validation (email, IP address, password strength, etc.)
#    - Performance measurement (timing, operations per second, etc.)
#    - Basic statistics (mean, median, standard deviation, etc.)
# 4. All functions are in the testlib namespace to avoid naming conflicts
# 5. The library is designed to be header-only compatible but implemented as static library
# 6. Compatible with both traditional C++ code and C++20 modules
