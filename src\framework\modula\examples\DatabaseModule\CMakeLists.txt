# DatabaseModule CMakeLists.txt
# 数据库模块 - 依赖CoreModule
# 用于并发初始化性能测试

# 条件1：通过declare_module()在构建系统中声明模块库
add_library(DatabaseModule)

set_target_properties(DatabaseModule PROPERTIES
    VERSION 1.0.0      # 设置库的实现版本
)

# 条件2：模块库中包含主模块文件（命名为<目录名>.module.ixx）
target_sources(DatabaseModule
    PUBLIC
        FILE_SET CXX_MODULES FILES
            DatabaseModule.module.ixx  # 符合<目录名>.module.ixx命名规范
    PRIVATE
        database_module_registration.cpp
)

# 链接到modula库和依赖模块
target_link_libraries(DatabaseModule PUBLIC modula CoreModule)

# 条件1：声明DatabaseModule为ModuleDeclaration管理的模块
declare_module(DatabaseModule)

# 设置C++标准
set_target_properties(DatabaseModule PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# ============================================================================
# Enhanced Encoding and Compiler Configuration for DatabaseModule
# ============================================================================

# Apply comprehensive encoding settings for better Unicode and Chinese support
if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(DatabaseModule PRIVATE
        /W4                     # High warning level
        /permissive-           # Strict conformance mode
        /utf-8                 # UTF-8 source and execution character sets
        /std:c++latest        # Latest C++ standard
        /experimental:module   # Enable C++ modules
        /bigobj               # Support large object files
        /Zc:__cplusplus       # Correct __cplusplus macro value
        /wd4819               # Disable specific encoding warning C4819
    )
    target_compile_definitions(DatabaseModule PRIVATE
        UNICODE                  # Enable Unicode support
        _UNICODE                 # Enable Unicode support
        _CRT_SECURE_NO_WARNINGS # Disable CRT security warnings
        NOMINMAX                # Prevent min/max macro conflicts
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(DatabaseModule PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules-ts            # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
        -fwide-exec-charset=UTF-32LE # Wide character set
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(DatabaseModule PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules               # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
    )
endif()

# 添加编译定义
target_compile_definitions(DatabaseModule PRIVATE
    MODULA_CXX_STANDARD=23
    MODULA_MODULES_AVAILABLE=1
    DATABASEMODULE_VERSION_MAJOR=1
    DATABASEMODULE_VERSION_MINOR=0
    DATABASEMODULE_VERSION_PATCH=0
)

# 设置模块输出目录
if(DEFINED CMAKE_CXX_MODULE_DIRECTORY)
    set_target_properties(DatabaseModule PROPERTIES
        CXX_MODULE_DIRECTORY "${CMAKE_CXX_MODULE_DIRECTORY}/DatabaseModule"
    )
endif()

# 导出DatabaseModule目标，使其可以被父项目使用
set_target_properties(DatabaseModule PROPERTIES
    EXPORT_NAME DatabaseModule
    OUTPUT_NAME DatabaseModule
)

# 可选：添加安装配置（如果需要安装DatabaseModule）
if(MODULA_INSTALL_TEST_MODULES)
    install(TARGETS DatabaseModule
        EXPORT DatabaseModuleTargets
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
        FILE_SET CXX_MODULES DESTINATION ${CMAKE_INSTALL_LIBDIR}/modules/DatabaseModule
    )
endif()

# 添加模块验证注释
# 此CMakeLists.txt展示了README.md中4个条件的完整实现：
# 条件1: ✓ declare_module(DatabaseModule) - CMake声明
# 条件2: ✓ DatabaseModule.module.ixx - 主模块文件命名规范
# 条件3: ✓ 在DatabaseModule.module.ixx中实现Module概念约束
# 条件4: ✓ 通过REGISTER_MODULE宏在database_module_registration.cpp中注册
