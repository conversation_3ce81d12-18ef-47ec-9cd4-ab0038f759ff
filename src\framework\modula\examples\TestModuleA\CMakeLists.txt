# TestModuleA CMakeLists.txt
# 符合README.md规范的示例模块配置
# 展示ModulaFramework v3.0的4个条件模块定义和依赖关系管理

# 条件1：通过declare_module()在构建系统中声明模块库
# 创建TestModuleA模块库（注意：库名与目录名一致）
add_library(TestModuleA)

# 条件2：模块库中包含主模块文件（命名为<目录名>.module.ixx）
target_sources(TestModuleA
    PUBLIC
        FILE_SET CXX_MODULES FILES
            TestModuleA.module.ixx  # 符合<目录名>.module.ixx命名规范
    # PRIVATE
    #     test_module_a_registration.cpp
)

# 链接到modula库以访问框架功能
target_link_libraries(TestModuleA PUBLIC modula)

# 链接到传统库 - TestLib
target_link_libraries(TestModuleA PUBLIC TestLib)

# 链接到依赖的模块 - TestModule
target_link_libraries(TestModuleA PUBLIC TestModule)

# 条件1：声明TestModuleA为ModuleDeclaration管理的模块
declare_module(TestModuleA)

# 设置C++标准
set_target_properties(TestModuleA PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# ============================================================================
# Enhanced Encoding and Compiler Configuration for TestModuleA
# ============================================================================

# Apply comprehensive encoding settings for better Unicode and Chinese support
if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(TestModuleA PRIVATE
        /W4                     # High warning level
        /permissive-           # Strict conformance mode
        /utf-8                 # UTF-8 source and execution character sets
        /std:c++latest        # Latest C++ standard
        /experimental:module   # Enable C++ modules
        /bigobj               # Support large object files
        /Zc:__cplusplus       # Correct __cplusplus macro value
        /wd4819               # Disable specific encoding warning C4819
    )
    target_compile_definitions(TestModuleA PRIVATE
        UNICODE                  # Enable Unicode support
        _UNICODE                 # Enable Unicode support
        _CRT_SECURE_NO_WARNINGS # Disable CRT security warnings
        NOMINMAX                # Prevent min/max macro conflicts
    )

    # 设置模块接口文件输出目录
    set_target_properties(TestModuleA PROPERTIES
        CXX_MODULE_DIRECTORY "${CMAKE_BINARY_DIR}/modules"
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(TestModuleA PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules-ts            # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
        -fwide-exec-charset=UTF-32LE # Wide character set
    )

    # GCC模块支持 (g++15+)
    if(CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL "15.0")
        target_compile_options(TestModuleA PRIVATE
            -fmodule-mapper="${CMAKE_BINARY_DIR}/modules/module.map"
        )
    endif()
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(TestModuleA PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules               # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
        -fmodule-cache-path="${CMAKE_BINARY_DIR}/modules"
    )
endif()

# 设置模块依赖关系
# 确保TestLib和TestModule在TestModuleA之前构建
add_dependencies(TestModuleA TestModule)

# 设置模块输出目录
set_target_properties(TestModuleA PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 添加编译定义
target_compile_definitions(TestModuleA PRIVATE
    TESTMODULEA_VERSION="1.0.0"
    TESTMODULEA_NAME="TestModuleA"
)

# 设置包含目录
target_include_directories(TestModuleA PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}"
    "${CMAKE_SOURCE_DIR}/modules"
)

# 添加模块验证注释
# 此CMakeLists.txt展示了README.md中4个条件的完整实现：
# 条件1: ✓ declare_module(TestModuleA) - CMake声明
# 条件2: ✓ TestModuleA.module.ixx - 主模块文件命名规范
# 条件3: ✓ 在TestModuleA.module.ixx中实现Module概念约束
# 条件4: ✓ 通过REGISTER_MODULE宏在test_module_a_registration.cpp中注册
#
# 依赖关系管理：
# - TestModuleA依赖TestModule
# - 通过target_link_libraries和add_dependencies确保正确的构建顺序
# - 通过优先级设置(60 < 75)确保正确的初始化顺序
