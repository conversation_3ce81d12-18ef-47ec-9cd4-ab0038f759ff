# ServiceModule CMakeLists.txt
add_library(ServiceModule)
set_target_properties(ServiceModule PROPERTIES VERSION 1.0.0)

target_sources(ServiceModule
    PUBLIC FILE_SET CXX_MODULES FILES ServiceModule.module.ixx
    PRIVATE service_module_registration.cpp
)

target_link_libraries(ServiceModule PUBLIC modula DatabaseModule NetworkModule)
declare_module(ServiceModule)

set_target_properties(ServiceModule PROPERTIES
    CXX_STANDARD 23 CXX_STANDARD_REQUIRED ON CXX_EXTENSIONS OFF
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(ServiceModule PRIVATE /W4 /permissive- /utf-8 /std:c++latest /experimental:module)
    target_compile_definitions(ServiceModule PRIVATE UNICODE _UNICODE _CRT_SECURE_NO_WARNINGS NOMINMAX)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(ServiceModule PRIVATE -Wall -Wextra -std=c++23 -fmodules-ts)
endif()

target_compile_definitions(ServiceModule PRIVATE MODULA_CXX_STANDARD=23 MODULA_MODULES_AVAILABLE=1)
