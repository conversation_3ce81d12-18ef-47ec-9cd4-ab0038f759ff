# Modula 框架 2.0: 设计与演进文档

## 1. 项目概述与目标

### 1.1. 现状回顾

Modula 框架当前版本（下称 v1）成功地验证了一个核心理念：**通过在构建时（CMake配置阶段）对代码进行自动化分析，将模块依赖关系等架构信息生成为C++代码，从而在运行时实现零开销的依赖图构建和拓扑排序**。

v1架构的核心工作流是：
`CMake声明 (declare_module)` -> `正则解析 (AnalyzerProcessor)` -> `JSON元数据` -> `Python代码生成` -> `编译期C++元数据` -> `运行时管理`

这个架构的优势在于其自动化和零运行时开销的特点。然而，它也存在一些固有的局限性，例如依赖脆弱的正则表达式解析、对Python的外部依赖、以及功能扩展性不足等。

### 1.2. 优化目标与愿景

Modula 2.0 的目标是 **从一个"模块化运行时库"演进为一个"集成式C++应用架构管理生态系统"**。

我们旨在提供一套完整的工具链，帮助开发者在项目的整个生命周期中（从设计、编码、编译到运行），都能清晰地定义、验证、可视化和管理其软件架构。最终愿景是让Modula成为构建复杂、高质量、大规模C++应用程序的基石。

## 2. 核心设计原则与理念

Modula 2.0 将遵循以下核心设计原则：

1.  **架构即代码 (Architecture as Code)**: 软件架构不应只存在于设计文档中，而应通过现代C++的声明式语法（如属性 `[[...]]`）在代码中直接表达，使其可被工具静态分析、验证和版本控制。

2.  **前置错误 (Shift-Left Error Detection)**: 尽最大可能将运行时可能出现的架构问题（如循环依赖、依赖缺失、接口不匹配）前置到编译前或编译期进行检测和报告，缩短反馈循环。

3.  **开发者体验优先 (Developer-Centric Tooling)**: 框架应提供强大的工具来提升开发效率，包括但不限于：架构可视化、实时架构规则校验、强大的依赖注入（DI）和简化的模块注册机制。

4.  **高性能与低侵入**: 保持v1的核心优势——极低的运行时开销。同时，框架的集成方式应尽可能对业务逻辑无感，避免引入不必要的复杂性。

5.  **高可扩展性**: 框架的核心分析引擎与运行时应解耦，并提供清晰的API，允许社区或团队根据自身需求进行功能扩展。

## 3. 优化后的架构设计 (Modula 2.0)

为了实现上述目标，我们提出一个基于 **AST（抽象语法树）分析** 的全新架构。

### 3.1. 新架构工作流

![Modula 2.0 Architecture Diagram](https://i.imgur.com/your-diagram-placeholder.png)  <!-- 这是一个占位符，实际应生成并链接一个图表 -->

新的工作流如下：

1.  **声明 (Declaration)**: 开发者在C++代码中，使用 **C++属性** 来声明模块及其元数据。
    ```cpp
    // in DatabaseModule.ixx
    [[modula::module(name = "Database", version = "1.0")]]
    [[modula::provides("IDatabaseService")]] // 声明该模块提供的服务接口
    export class DatabaseModule : public modula::IModule { ... };
    
    // in ServiceModule.ixx
    [[modula::module(name = "Service")]]
    [[modula::requires("IDatabaseService")]] // 声明该模块需要的服务接口
    export class ServiceModule : public modula::IModule { ... };
    ```

2.  **静态分析 (Static Analysis)**: 在构建过程中，CMake会调用一个 **独立的、基于Clang LibTooling的静态分析工具 (`modula-cli`)**。这个工具将：
    *   **解析C++代码**: 不再使用正则表达式，而是完整地解析C++代码，构建AST。
    *   **提取元数据**: 精确地从`[[modula::...]]`属性中提取模块名、版本、依赖、提供的服务等丰富信息。
    *   **架构验证**: 在编译前即可进行深入的架构验证，如检查`requires`的服务是否被某个依赖`provides`，甚至可以进行更复杂的架构规则检查。

3.  **产物生成 (Artifact Generation)**: `modula-cli` 分析完成后，会生成多种产物：
    *   **`modula.generated.cpp`**: 包含编译期元数据的C++源文件，用于实现零开销运行时。
    *   **`architecture.json` / `architecture.bin`**: 一份详细的架构描述文件，可供IDE插件、可视化工具或其他外部工具使用。
    *   **`graph.dot`**: Graphviz描述文件，用于一键生成架构依赖图。

4.  **编译 (Compilation)**: 生成的`modula.generated.cpp`与业务代码一同被编译，将所有架构信息无缝嵌入到最终的可执行文件中。

5.  **运行时 (Runtime)**: Modula 2.0的运行时得到极大增强：
    *   **依赖注入容器**: `ModuleManager` 内置一个轻量级的依赖注入（DI）容器。
    *   **服务定位**: 当初始化`ServiceModule`时，`ModuleManager`能够自动从依赖项（`DatabaseModule`）中找到并注入其需要的`IDatabaseService`实例。
    *   **并发初始化**: `DependencyGraph`可以分析出图中不相关的分支，并安全地并发初始化它们，大幅提升大型应用的启动速度。

## 4. 主要组件和模块说明

### 4.1. `modula-cli`: 静态分析与代码生成器

-   **技术栈**: C++、LLVM/Clang LibTooling。
-   **核心职责**:
    -   解析C++项目源文件。
    -   识别并解析 `[[modula::...]]` 属性。
    -   执行架构规则验证。
    -   生成C++元数据代码和多种格式的架构描述文件。
-   **优势**: 结果精确、功能强大、可独立于构建系统运行。

### 4.2. `modula-core`: 增强型运行时库

-   **核心职责**:
    -   **`ModuleManager`**: 包含DI容器，负责模块的实例化、服务注入和生命周期管理。
    -   **`DependencyGraph`**: 支持并发初始化调度，提供更丰富的图分析API。
    -   **`modula::attributes`**: 定义所有 `[[modula::...]]` 属性的头文件。
    -   **`IService` / `ServiceRegistry`**: 服务抽象与注册机制，实现模块间基于接口的通信。

### 4.3. 构建系统集成 (CMake/...)

-   **核心职责**: 提供简洁的CMake函数（如`modula_analyze_project()`），用于在构建流程中无缝地调用`modula-cli`并处理其输出。设计上会考虑未来对其他构建系统（如Bazel）的支持。

### 4.4. IDE插件 (未来)

-   **核心职责**:
    -   通过读取`modula-cli`生成的`architecture.json`文件，实现：
        -   在编辑器中实时可视化模块依赖图。
        -   当代码违反了`[[modula::...]]`中声明的架构规则时，实时显示错误或警告。
        -   提供代码补全，例如自动为`[[modula::requires(...)]]`建议可用的服务。

## 5. 与现有版本的改进对比

| 特性 | Modula v1 (Regex-based) | Modula 2.0 (AST-based) | 优势 |
| :--- | :--- | :--- | :--- |
| **解析引擎** | CMake + 正则表达式 | 独立C++工具 + Clang LibTooling | **健壮性、精确性**：能理解C++语法，不受代码格式影响。 |
| **模块声明** | `declare_module` (CMake) | `[[modula::module]]` (C++) | **架构即代码**：架构定义回归源代码，IDE友好，更直观。 |
| **依赖声明** | `import` 语句 (隐式) | `[[modula::requires]]` (显式) | **意图明确**：清晰表达模块间的服务依赖，而非不稳定的代码导入关系。 |
| **功能** | 依赖排序、生命周期管理 | 所有v1功能 + **DI、服务发现、并发初始化、架构校验** | **功能强大**：从一个启动器演变为真正的架构框架。 |
| **工具链** | 仅运行时库 | 运行时库 + `modula-cli` + (未来)IDE插件 | **生态完整**：覆盖从编码到运行的全过程，极大提升DX。 |
| **可扩展性** | 困难，需修改CMake处理器 | **高**，可通过编写新的LibTooling检查器或分析`modula-cli`输出来扩展 | 易于添加新的架构规则和工具。 |
| **外部依赖** | Python | LLVM/Clang (作为开发依赖) | 移除了运行时对Python的依赖，部署更简洁。 |

## 6. 实施建议和路线图

建议分三阶段实施Modula 2.0的演进：

### **第一阶段：核心引擎替换 (Q1-Q2)**
-   [ ] **开发`modula-cli` v0.1**: 实现基于LibTooling的基础功能，能够解析`[[modula::module]]`并生成与v1兼容的C++元数据代码。
-   [ ] **改造CMake集成**: 修改`ModulaDeclaration.cmake`等文件，使其调用`modula-cli`替代旧的Python和Regex流程。
-   **目标**: 在功能上对齐v1，但底层替换为更稳健的AST分析引擎。此阶段用户代码改动最小。

### **第二阶段：运行时增强与新特性 (Q3)**
-   [ ] **实现`[[modula::requires/provides]]`**：在`modula-cli`中增加对服务依赖属性的解析。
-   [ ] **增强`modula-core`**: 在`ModuleManager`中引入服务注册表和DI容器。
-   [ ] **实现并发初始化**: 在`DependencyGraph`中增加并发调度逻辑。
-   **目标**: 交付Modula 2.0的核心新功能，提供显著的价值提升。

### **第三阶段：生态系统与开发者工具 (Q4及以后)**
-   [ ] **开发Graphviz可视化**: `modula-cli`增加`--emit-dot`选项，一键生成依赖图。
-   [ ] **开发VS Code插件 v0.1**: 读取`architecture.json`，实现基础的架构可视化。
-   [ ] **完善文档**: 撰写详细的Modula 2.0用户和开发者文档。
-   **目标**: 打造完整的开发者生态，提升框架的易用性和吸引力。 