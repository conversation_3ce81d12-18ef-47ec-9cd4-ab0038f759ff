#!/usr/bin/env python3
"""
@file config.py
@brief Modula Generator - Configuration Module

Simple configuration management for the modula generator.
"""

from pathlib import Path
from typing import Optional
from dataclasses import dataclass

# ConfigurationError will be imported from processing when needed


@dataclass
class Config:
    """
    @brief Configuration data class

    Contains all configuration options needed by the generator.
    """

    # Basic configuration
    json_file: Optional[str] = None  #< Path to JSON metadata file
    output_directory: str = "modula_generated"  #< Output directory for generated files
    verbose: bool = False  #< Enable verbose output

    # File naming options
    main_module_filename: str = "modula.generated.ixx"  #< Main module interface filename
    companion_file_suffix: str = ".gen.cpp"  #< Companion file suffix

    def __post_init__(self):
        """
        @brief Post-initialization processing

        Normalizes file paths to absolute paths.
        """
        if self.json_file:
            self.json_file = str(Path(self.json_file).resolve())

        self.output_directory = str(Path(self.output_directory).resolve())

    def validate(self) -> bool:
        """
        @brief Validate configuration

        @return True if configuration is valid, False otherwise
        @throws ValueError if validation fails
        """
        # Validate required parameters
        if not self.json_file:
            raise ValueError("json_file must be specified")

        if not Path(self.json_file).exists():
            raise ValueError(f"JSON file not found: {self.json_file}")

        if not Path(self.json_file).is_file():
            raise ValueError(f"JSON file path is not a file: {self.json_file}")

        # Validate output directory
        if not self.output_directory:
            raise ValueError("output_directory cannot be empty")

        output_path = Path(self.output_directory)
        if output_path.exists() and not output_path.is_dir():
            raise ValueError(f"Output path exists but is not a directory: {self.output_directory}")

        return True
