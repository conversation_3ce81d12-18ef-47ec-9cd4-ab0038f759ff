#]=======================================================================]
function(_modula_analyzer_apply_cached_result target_name cached_result)
    string(REPLACE ":" ";" result_parts "${cached_result}")
    list(GET result_parts 0 module_name)

    list(LENGTH result_parts result_parts_len)

    # Handle imports and exports safely
    if(result_parts_len GREATER 1)
        list(GET result_parts 1 imports)
    else()
        set(imports "")
    endif()
    
    if(result_parts_len GREATER 2)
        list(GET result_parts 2 exports)
    else()
        set(exports "")
    endif()

    _modula_analyzer_set_target_properties("${target_name}" "${module_name}" "${imports}" "${exports}")
endfunction()

# ==============================================================================
# Target Analysis 