# ==============================================================================
# ModulaConfiguration.cmake - Configuration Management
# ==============================================================================
#
# Provides centralized configuration management for the Modula Framework.
# Handles default configuration, environment variables, and validation.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

# ==============================================================================
# Configuration State Management
# ==============================================================================

# Global configuration state
set(_MODULA_CONFIG_INITIALIZED FALSE CACHE INTERNAL "Configuration system initialization flag")

# ==============================================================================
# Default Configuration Values
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_define_default_config

  Define default configuration values for the Modula Framework.

  Sets up essential configuration options with reasonable defaults.
#]=======================================================================]
function(_modula_define_default_config)
    # Core system configuration
    if(NOT DEFINED MODULA_ENABLE_VERBOSE_OUTPUT)
        set(MODULA_ENABLE_VERBOSE_OUTPUT ON CACHE BOOL "Enable verbose output")
    endif()

    # Module discovery configuration
    if(NOT DEFINED MODULA_MODULE_EXTENSIONS)
        set(MODULA_MODULE_EXTENSIONS ".ixx;.cppm;.mpp;.hpp;.h" CACHE STRING
            "Supported module file extensions")
    endif()

    if(NOT DEFINED MODULA_MODULE_NAMING_PATTERNS)
        set(MODULA_MODULE_NAMING_PATTERNS "<module_name>.module;<module_name>_module;<module_name>" CACHE STRING
            "Module file naming patterns in priority order. Use <module_name> as placeholder.")
    endif()

    # Build optimization configuration
    if(NOT DEFINED MODULA_ENABLE_INCREMENTAL_BUILD)
        set(MODULA_ENABLE_INCREMENTAL_BUILD ON CACHE BOOL "Enable incremental build and caching")
    endif()

    # Metadata generation configuration

    if(NOT DEFINED MODULA_OUTPUT_DIR)
        set(MODULA_OUTPUT_DIR "${CMAKE_BINARY_DIR}/modula_generated" CACHE PATH
            "Directory for generated files")
    endif()

    if(NOT DEFINED MODULA_ENABLE_JSON_METADATA)
        set(MODULA_ENABLE_JSON_METADATA ON CACHE BOOL
            "Enable JSON metadata file generation")
    endif()

    if(NOT DEFINED MODULA_METADATA_JSON_FILENAME)
        set(MODULA_METADATA_JSON_FILENAME "modules_metadata.json" CACHE STRING
            "Filename for generated JSON metadata")
    endif()

    if(NOT DEFINED MODULA_METADATA_GENERATOR_SCRIPT)
        set(MODULA_METADATA_GENERATOR_SCRIPT "${CMAKE_CURRENT_SOURCE_DIR}/tools/modula_generator" CACHE STRING
            "Directory for generator tools")
    endif()
endfunction()

# ==============================================================================
# Environment Variable Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_load_environment_config

  Load configuration from environment variables.

  Checks for relevant environment variables and overrides default
  configuration values when found.
#]=======================================================================]
function(_modula_load_environment_config)
    # Environment variable mappings: ENV_VAR:CMAKE_VAR:TYPE
    set(env_mappings
        "MODULA_VERBOSE:MODULA_ENABLE_VERBOSE_OUTPUT:BOOL"
        "MODULA_OUTPUT_DIR:MODULA_OUTPUT_DIR:PATH"
        "MODULA_JSON_METADATA:MODULA_ENABLE_JSON_METADATA:BOOL"
        "MODULA_INCREMENTAL_BUILD:MODULA_ENABLE_INCREMENTAL_BUILD:BOOL"
    )

    foreach(mapping ${env_mappings})
        string(REPLACE ":" ";" mapping_parts "${mapping}")
        list(GET mapping_parts 0 env_var)
        list(GET mapping_parts 1 cmake_var)
        list(GET mapping_parts 2 var_type)

        if(DEFINED ENV{${env_var}})
            set(env_value "$ENV{${env_var}}")

            # Type-specific conversion and validation
            if(var_type STREQUAL "BOOL")
                if(env_value MATCHES "^(1|ON|TRUE|YES)$")
                    set(${cmake_var} ON CACHE ${var_type} "Set from environment variable ${env_var}" FORCE)
                elseif(env_value MATCHES "^(0|OFF|FALSE|NO)$")
                    set(${cmake_var} OFF CACHE ${var_type} "Set from environment variable ${env_var}" FORCE)
                else()
                    message(WARNING "ModuleConfiguration: Invalid boolean value '${env_value}' for ${env_var}")
                endif()
            else()
                set(${cmake_var} "${env_value}" CACHE ${var_type} "Set from environment variable ${env_var}" FORCE)
            endif()
        endif()
    endforeach()
endfunction()

# ==============================================================================
# Configuration Validation
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_validate_config

  Validate configuration values for correctness.

  Checks essential configuration options for validity.
#]=======================================================================]
function(_modula_validate_config)
    # Validate output directory
    if(NOT MODULA_OUTPUT_DIR)
        message(FATAL_ERROR "ModulaConfiguration: MODULA_OUTPUT_DIR cannot be empty")
    endif()

    # Validate module extensions
    if(NOT MODULA_MODULE_EXTENSIONS)
        message(FATAL_ERROR "ModulaConfiguration: MODULA_MODULE_EXTENSIONS cannot be empty")
    endif()

    # Validate JSON metadata configuration
    if(MODULA_ENABLE_JSON_METADATA AND NOT MODULA_METADATA_JSON_FILENAME)
        message(FATAL_ERROR "ModulaConfiguration: MODULA_METADATA_JSON_FILENAME cannot be empty when JSON metadata is enabled")
    endif()

    # Validate module naming patterns
    if(NOT MODULA_MODULE_NAMING_PATTERNS)
        message(FATAL_ERROR "ModulaConfiguration: MODULA_MODULE_NAMING_PATTERNS cannot be empty")
    endif()
endfunction()

# ==============================================================================
# Directory Management
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_ensure_output_directory

  Ensure the output directory exists and is writable.

  Creates the necessary output directory structure for generated files.
#]=======================================================================]
function(_modula_ensure_output_directory)
    if(NOT EXISTS "${MODULA_OUTPUT_DIR}")
        file(MAKE_DIRECTORY "${MODULA_OUTPUT_DIR}")
        if(NOT EXISTS "${MODULA_OUTPUT_DIR}")
            message(FATAL_ERROR
                "ModulaConfiguration: Failed to create output directory: ${MODULA_OUTPUT_DIR}")
        endif()
    endif()
endfunction()

# ==============================================================================
# Public Configuration Interface
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_get_config

  Get a configuration value.

  .. code-block:: cmake

    modula_get_config(<config_name> <output_var>)

  ``config_name``
    Configuration option name
  ``output_var``
    Variable name to store the configuration value

  Retrieves the current value of a configuration option. Automatically
  initializes the configuration system if not already initialized.
#]=======================================================================]
function(modula_get_config config_name output_var)
    if(NOT _MODULA_CONFIG_INITIALIZED)
        _modula_define_default_config()
        _modula_load_environment_config()
        _modula_validate_config()
        _modula_ensure_output_directory()
        set(_MODULA_CONFIG_INITIALIZED TRUE CACHE INTERNAL "Configuration initialized" FORCE)
    endif()

    if(DEFINED ${config_name})
        set(${output_var} "${${config_name}}" PARENT_SCOPE)
    else()
        message(WARNING "ModulaConfiguration: Unknown config option '${config_name}'")
        set(${output_var} "" PARENT_SCOPE)
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_set_config

  Set a configuration value.

  .. code-block:: cmake

    modula_set_config(<config_name> <value> [TYPE <type>])

  ``config_name``
    Configuration option name
  ``value``
    Configuration value
  ``TYPE``
    Configuration type (BOOL, STRING, PATH). Default: STRING

  Sets a configuration option to the specified value.
#]=======================================================================]
function(modula_set_config config_name value)
    set(options "")
    set(one_value_args TYPE)
    set(multi_value_args "")
    cmake_parse_arguments(SET_CONFIG "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})

    if(NOT SET_CONFIG_TYPE)
        set(SET_CONFIG_TYPE STRING)
    endif()

    set(${config_name} "${value}" CACHE ${SET_CONFIG_TYPE} "User configured value" FORCE)
endfunction()

message(STATUS "Modula: Configuration management loaded")
