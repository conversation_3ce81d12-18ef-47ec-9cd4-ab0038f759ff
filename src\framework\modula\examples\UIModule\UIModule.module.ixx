/**
 * @file UIModule.module.ixx
 * @brief UIModule - 用户界面模块，依赖ServiceModule和LoggingModule
 */

module;
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <vector>

export module UIModule;
import ServiceModule;
import LoggingModule;

export class UIModule {
public:
    UIModule() = default;
    ~UIModule() { if (initialized_) shutdown(); }

    bool initialize() {
        if (initialized_) return true;
        std::cout << "[UIModule] 开始初始化用户界面..." << std::endl;
        initialization_time_ = std::chrono::steady_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(280));
        initialized_ = true;
        std::cout << "[UIModule] 用户界面初始化完成" << std::endl;
        return true;
    }

    void shutdown() {
        if (!initialized_) return;
        std::cout << "[UIModule] 关闭用户界面..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(120));
        initialized_ = false;
        std::cout << "[UIModule] 用户界面关闭完成" << std::endl;
    }

    bool is_initialized() const noexcept { return initialized_; }
    std::string get_version() const { return "1.0.0"; }
    std::vector<std::string> get_dependencies() const { return {"ServiceModule", "LoggingModule"}; }
    bool supports_hot_reload() const noexcept { return false; }
    auto get_initialization_time() const noexcept { return initialization_time_; }

    void show_ui() {
        if (!initialized_) throw std::runtime_error("UIModule not initialized");
        std::cout << "[UIModule] 显示用户界面" << std::endl;
    }

    void handle_user_input(const std::string& input) {
        if (!initialized_) throw std::runtime_error("UIModule not initialized");
        std::cout << "[UIModule] 处理用户输入: " << input << std::endl;
    }

    bool is_ui_ready() const noexcept { return initialized_; }

private:
    bool initialized_ = false;
    std::chrono::steady_clock::time_point initialization_time_;
};
