#!/usr/bin/env python3
"""
@file generator.py
@brief Main Modula Generator class

Enhanced main generator with improved architecture and dependency analysis.
"""

import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

from .config import Config
from .core import (
    <PERSON>son<PERSON>ataReader,
    DependencyAnalyzer,
    CppCodeGenerator,
    ModulaGeneratorError,
    ConfigurationError,
    GenerationError
)


class ModulaGenerator:
    """
    @brief Enhanced Modula Generator class
    
    Provides comprehensive C++ metadata generation with dependency analysis.
    """
    
    def __init__(self) -> None:
        """
        @brief Initialize the generator with enhanced capabilities
        """
        self.logger = logging.getLogger("modula_generator")
        self._modules: List[Dict[str, Any]] = []
        self._dependency_result: Optional[Dict[str, Any]] = None
        self._statistics = {
            'modules_processed': 0,
            'files_generated': 0,
            'processing_time': 0.0,
            'dependency_analysis': None,
            'generation_statistics': None
        }
        self._start_time = 0.0

    def generate(self, config: Config) -> bool:
        """
        @brief Execute code generation with enhanced processing

        @param config Generation configuration
        @return True if generation succeeded, False otherwise
        """
        try:
            self._start_time = time.time()
            
            # Validate configuration
            config.validate()

            # Read input data
            self._read_data(config)

            # Generate code
            self._generate_code(config)
            
            # Update final statistics
            self._statistics['processing_time'] = time.time() - self._start_time

            self.logger.info("Code generation completed successfully")
            return True

        except (ValueError, ConfigurationError, GenerationError) as e:
            self.logger.error(f"Generation failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during generation: {e}")
            return False

    def generate_from_file(self, json_file: str, output_dir: str, **kwargs) -> bool:
        """Generate code from file (convenience method)

        Args:
            json_file: JSON metadata file path
            output_dir: Output directory
            **kwargs: Additional configuration options

        Returns:
            True if generation succeeded, False otherwise
        """
        try:
            config = Config(
                json_file=json_file,
                output_directory=output_dir,
                **kwargs
            )
            return self.generate(config)
        except Exception as e:
            self.logger.error(f"Failed to generate from file {json_file}: {e}")
            return False

    def validate_json_file(self, json_file: str) -> List[str]:
        """Validate JSON file format

        Args:
            json_file: JSON file path

        Returns:
            List of validation errors (empty if valid)
        """
        errors = []

        try:
            if not Path(json_file).exists():
                errors.append(f"JSON file not found: {json_file}")
                return errors

            reader = JsonDataReader(json_file)
            modules = reader.read_modules()

            if not modules:
                errors.append("No modules found in JSON file")
                
            # Validate dependencies
            analyzer = DependencyAnalyzer(modules)
            result = analyzer.analyze()
            
            if result.get('has_cycles', False):
                cycles = result.get('cycles', [])
                for cycle in cycles:
                    errors.append(f"Circular dependency detected: {' -> '.join(cycle)}")

        except Exception as e:
            errors.append(f"Failed to validate JSON file: {e}")

        return errors

    def _read_data(self, config: Config) -> None:
        """Read input data with enhanced processing"""
        try:
            self.logger.info(f"Reading modules from: {config.json_file}")

            reader = JsonDataReader(config.json_file)
            self._modules = reader.read_modules()
            
            # Perform dependency analysis
            self.logger.info("Performing dependency analysis...")
            analyzer = DependencyAnalyzer(self._modules)
            self._dependency_result = analyzer.analyze()
            
            # Check for circular dependencies
            if self._dependency_result.get('has_cycles', False):
                cycles = self._dependency_result.get('cycles', [])
                cycle_strs = [' -> '.join(cycle) for cycle in cycles]
                raise GenerationError(f"Circular dependencies detected: {'; '.join(cycle_strs)}")
            
            # Update statistics
            self._statistics['modules_processed'] = len(self._modules)
            self._statistics['dependency_analysis'] = self._dependency_result

            self.logger.info(f"Successfully read {len(self._modules)} modules")
            if self._dependency_result:
                order = ', '.join(self._dependency_result.get('initialization_order', []))
                self.logger.info(f"Dependency analysis completed. Order: [{order}]")

        except Exception as e:
            raise GenerationError(f"Failed to read input data: {e}")

    def _generate_code(self, config: Config) -> None:
        """Generate code files with enhanced capabilities"""
        try:
            if not self._modules:
                self.logger.warning("No modules to generate code for")
                return

            self.logger.info(f"Generating code for {len(self._modules)} modules")

            # Create output directory
            output_path = Path(config.output_directory)
            output_path.mkdir(parents=True, exist_ok=True)

            # Generate code
            generator = CppCodeGenerator(config)
            success = generator.generate_all(self._modules, self._dependency_result)

            if not success:
                raise GenerationError("Code generation failed")
            
            # Update statistics
            gen_stats = generator.get_statistics()
            self._statistics['files_generated'] = gen_stats['files_generated']
            self._statistics['generation_statistics'] = gen_stats

            self.logger.info("Code generation completed successfully")

        except Exception as e:
            raise GenerationError(f"Code generation failed: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get generation statistics"""
        return self._statistics.copy()
    
    def get_modules(self) -> List[Dict[str, Any]]:
        """Get processed modules"""
        return self._modules.copy()
    
    def get_dependency_result(self) -> Optional[Dict[str, Any]]:
        """Get dependency analysis result"""
        return self._dependency_result


# Convenience functions
def generate_from_json(json_file: str, output_dir: str, **kwargs) -> bool:
    """Convenience function to generate code from JSON file
    
    Args:
        json_file: Path to JSON metadata file
        output_dir: Output directory for generated files
        **kwargs: Additional configuration options
        
    Returns:
        True if generation succeeded, False otherwise
    """
    generator = ModulaGenerator()
    return generator.generate_from_file(json_file, output_dir, **kwargs)
