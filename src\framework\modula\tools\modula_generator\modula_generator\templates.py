#!/usr/bin/env python3
"""
Modula Generator - Template Management System

C++ code template management with full metadata support.
"""

import logging
from typing import Dict, List, Any
from datetime import datetime


class TemplateManager:
    """Template manager for C++ code generation with full metadata support"""

    def __init__(self):
        self.logger = logging.getLogger("template_manager")

    def render_main_interface(self, context: Dict[str, Any]) -> str:
        """Render main module interface file with full metadata support"""
        modules = context.get('modules', [])
        generated_at = context.get('generated_at', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Generate module list
        module_names = []
        for module in modules:
            module_name = module.get('name', module.get('target_name', 'unknown'))
            module_names.append(module_name)

        # Generate dependency analysis
        dependency_info = self._generate_dependency_info(modules)
        parallel_groups = self._generate_parallel_groups(modules)

        # Create main interface content with full metadata support
        content = f"""/**
 * @file modula.generated.ixx
 * @brief Global compile-time metadata registry and dependency information
 *
 * Generated at: {generated_at}
 * Generator version: 1.0.0
 *
 * This file was generated by a script and any modifications will be overwritten.
 * DO NOT EDIT MANUALLY
 *
 * This file contains only compile-time metadata and type information.
 * Actual initialization logic is implemented in ModuleManager to avoid circular dependencies.
 */

module;

#include <string_view>
#include <array>
#include <span>

export module modula.generated;

import modula.types;
import modula.metadata;
// 模块元数据导入已禁用，避免编译错误

export namespace modula::generated {{

// ============================================================================
// 基础模块信息
// ============================================================================

/**
 * @brief Total number of registered modules
 */
inline constexpr std::size_t total_modules = {len(modules)};

/**
 * @brief Array of all module names
 */
inline constexpr std::array<std::string_view, {len(modules)}> module_names = {{
    {', '.join(f'"{name}"' for name in module_names)}
}};

/**
 * @brief 模块前向声明（避免循环依赖）
 */
{self._generate_forward_declarations(modules)}

// ============================================================================
// 编译期类型化功能（基于现代C++20/23）
// ============================================================================


// ============================================================================
// 函数模板声明（用于特化）
// ============================================================================

/**
 * @brief 获取模块元数据的函数模板
 * @tparam T 模块类型
 * @return 模块的元数据信息
 */
template<typename T>
constexpr modula::Metadata get_module_metadata();

/**
 * @brief 获取模块类型标签的函数模板
 * @tparam T 模块类型
 * @return 模块的类型标签
 */
template<typename T>
constexpr std::size_t meta_type_tag();

/**
 * @brief 获取模块类型索引的函数模板
 * @tparam T 模块类型
 * @return 模块的类型索引
 */
template<typename T>
constexpr std::size_t meta_type_index();

/**
 * @brief 检查模块是否已注册的函数模板
 * @tparam T 模块类型
 * @return 是否已注册
 */
template<typename T>
constexpr bool is_type_registered();

{dependency_info}

{parallel_groups}

// ============================================================================
// 模块信息输出和调试功能
// ============================================================================

/**
 * @brief 获取模块信息摘要
 * @return 包含所有模块基本信息的字符串视图数组
 */
consteval auto get_module_summary() noexcept {{
    return module_names;
}}

/**
 * @brief 获取依赖关系摘要
 * @return 依赖关系的编译期描述
 */
consteval std::string_view get_dependency_summary() noexcept {{
    return "Generated dependency information available at compile time";
}}

/**
 * @brief 编译期模块统计信息
 */
struct module_statistics {{
    static constexpr std::size_t total_count = total_modules;
    static constexpr std::size_t max_dependency_depth = {self._calculate_max_depth(modules)};
    static constexpr bool has_parallel_groups = {str(self._has_parallel_groups(modules)).lower()};
    static constexpr std::string_view generation_time = "{generated_at}";
}};

// ============================================================================
// 模块元数据访问接口
// ============================================================================

/**
 * @brief 获取所有模块的名称列表
 * @return 模块名称的编译期数组
 */
consteval auto get_all_module_names() noexcept {{
    return module_names;
}}

/**
 * @brief 检查模块是否存在
 * @param module_name 模块名称
 * @return 如果模块存在返回true
 */
consteval bool module_exists(std::string_view module_name) noexcept {{
    for (const auto& name : module_names) {{
        if (name == module_name) {{
            return true;
        }}
    }}
    return false;
}}

/**
 * @brief 获取模块索引
 * @param module_name 模块名称
 * @return 模块索引，如果不存在返回SIZE_MAX
 */
consteval std::size_t get_module_index(std::string_view module_name) noexcept {{
    for (std::size_t i = 0; i < module_names.size(); ++i) {{
        if (module_names[i] == module_name) {{
            return i;
        }}
    }}
    return SIZE_MAX;
}}

}} // namespace modula::generated
"""
        return content

    def render_companion_file(self, context: Dict[str, Any]) -> str:
        """Render companion .gen.cpp file for a module with full metadata support"""
        module = context.get('module', {})
        module_name = context.get('module_name', 'unknown')
        generated_at = context.get('generated_at', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Extract module information
        version = module.get('version', '1.0.0')
        directory = module.get('directory', '')
        interface_file = module.get('interface_file', f'{module_name}.module.ixx')
        dependencies = module.get('dependencies', [])
        sources = module.get('sources', [])
        parallel_group = module.get('parallel_group', 0)
        priority = module.get('priority', 0)

        # Generate arrays
        dependencies_array = self._generate_dependencies_array(dependencies)
        source_files_array = self._generate_source_files_array(sources)

        # Generate module import statement based on interface file type
        module_import_statement = self._generate_module_import_statement(module)

        # Create companion file content with full metadata support
        content = f"""/**
 * @file {module_name}.gen.cpp
 * @brief Module metadata implementation for {module_name}
 *
 * This file provides the concrete metadata for the {module_name} module.
 * It defines a specialization of the `get_generated_metadata` function template,
 * which is the sole source of truth for this module's metadata.
 *
 * This file is linked into the final executable when the module is used,
 * allowing the framework to access its metadata at runtime without static constructors.
 *
 * Generated at: {generated_at}
 * Generator version: 1.0.0
 *
 * DO NOT EDIT MANUALLY - MODIFICATIONS WILL BE OVERWRITTEN
 */

#include <string_view>
#include <array>
#include <span>

import modula.metadata;

// Import the module's interface to get the full type definition for the template specialization.
{module_import_statement}

// ============================================================================
// 模块元数据实现
// ============================================================================

namespace modula::metadata {{

namespace detail {{
    /**
     * @brief {module_name}的静态元数据常量
     */
    inline constexpr std::string_view module_name = "{module_name}";
    inline constexpr std::string_view module_target_name = "{module_name}";
    inline constexpr std::string_view module_version = "{self._escape_cpp_string(version)}";
    inline constexpr std::string_view module_directory = "{self._escape_cpp_string(directory)}";
    inline constexpr std::string_view module_primary_file = "{self._escape_cpp_string(interface_file)}";
    inline constexpr std::string_view module_build_timestamp = "{generated_at}";

    // 依赖数组
    inline constexpr std::array<std::string_view, {len(dependencies)}> module_dependencies = {{
{dependencies_array}
    }};

    // 源文件数组
    inline constexpr std::array<std::string_view, {len(sources)}> module_source_files = {{
{source_files_array}
    }};
}}

/**
 * @brief {module_name}的元数据函数模板特化
 */
/*template<>
inline constexpr modula::Metadata module_metadata<{module_name}>{{
    detail::module_name,
    detail::module_target_name,
    detail::module_version,
    detail::module_directory,
    detail::module_primary_file,
    detail::module_build_timestamp,
    std::span<const std::string_view>{{detail::module_dependencies}},
    std::span<const std::string_view>{{detail::module_source_files}},
    {parallel_group},
    {priority}
}};*/

// ============================================================================
// 运行时元数据定义（原gen.cpp功能）
// ============================================================================

// 编译时元数据详细定义
namespace detail {{
    // 依赖数组（运行时访问）
    inline constexpr std::array<std::string_view, {len(dependencies)}> runtime_module_dependencies = {{
{dependencies_array}
    }};

    // 源文件数组（运行时访问）
    inline constexpr std::array<std::string_view, {len(sources)}> runtime_module_source_files = {{
{source_files_array}
    }};
}}

}} // namespace modula::metadata

// ============================================================================
// 元数据注册机制实现
// ============================================================================

namespace {{

/**
 * @brief {module_name}模块的静态元数据定义
 */
static constexpr std::array<std::string_view, {len(dependencies)}> dependencies_array{{{{
{dependencies_array}
}}}};

static constexpr std::array<std::string_view, {len(sources)}> source_files_array{{{{
{source_files_array}
}}}};

static constexpr modula::Metadata {module_name}_metadata {{
    /* name */            "{module_name}",
    /* target_name */     "{module_name}",
    /* version */         "{self._escape_cpp_string(version)}",
    /* directory */       "{self._escape_cpp_string(directory)}",
    /* primary_file */    "{self._escape_cpp_string(interface_file)}",
    /* build_timestamp */ "{generated_at}",
    /* dependencies */    std::span<const std::string_view>{{dependencies_array}},
    /* source_files */    std::span<const std::string_view>{{source_files_array}},
    /* parallel_group */  {parallel_group},
    /* priority */        {priority}
}};

/**
 * @brief {module_name}模块的自动注册器
 *
 * 这个类在静态初始化时自动注册模块元数据，确保在全归档链接时
 * 所有生成的元数据都能被正确注册到系统中。
 *
 * 注册时机：程序启动时，在main函数执行之前
 * 注册方式：调用MetadataRegistry::register_metadata()主动注册
 * 线程安全：静态初始化保证单次执行
 */
class {module_name}AutoRegistrar {{
public:
    {module_name}AutoRegistrar() {{
        // 使用MetadataRegistry进行主动注册
        // 这确保了在modula::register_module<T>()被调用之前，元数据就已经可用
        bool success = modula::MetadataRegistry<{module_name}>::register_metadata({module_name}_metadata);

        // 在调试模式下输出注册状态（可选）
        #ifdef _DEBUG
        if (success) {{
            // 注册成功，元数据现在可通过MetadataRegistry访问
        }} else {{
            // 元数据已经注册过了，这是正常情况
        }}
        #endif
    }}
}};

// 静态实例，确保在程序启动时自动注册
// 这个全局对象的构造函数会在main()函数之前执行
static {module_name}AutoRegistrar {module_name}_auto_registrar;

}} // anonymous namespace

"""
        return content

    def _generate_forward_declarations(self, modules: List[Dict[str, Any]]) -> str:
        """Generate forward declarations for modules"""
        declarations = []
        for module in modules:
            module_name = module.get('name', module.get('target_name', 'unknown'))
            declarations.append(f"class {module_name};")
        return '\n'.join(declarations)

    def _generate_dependency_info(self, modules: List[Dict[str, Any]]) -> str:
        """Generate dependency information"""
        # Generate initialization order
        ordered_modules = self._sort_modules_by_dependencies(modules)

        # Generate type list
        type_list = ', '.join(ordered_modules)

        dependency_info = f"""/**
 * @brief 类型化的初始化顺序
 */
using initialization_order = type_list<{type_list}>;

/**
 * @brief 类型化的依赖关系定义
 */
template<typename T>
struct module_dependencies {{
    using dependencies = type_list<>;  // 默认无依赖
}};"""

        # Generate specific dependencies
        for module in modules:
            module_name = module.get('name', module.get('target_name', 'unknown'))
            dependencies = module.get('dependencies', [])
            if dependencies:
                deps_list = ', '.join(dependencies)
                dependency_info += f"""

template<>
struct module_dependencies<{module_name}> {{
    using dependencies = type_list<{deps_list}>;
}};"""

        return dependency_info

    def _generate_parallel_groups(self, modules: List[Dict[str, Any]]) -> str:
        """Generate parallel group information"""
        # Group modules by parallel_group
        groups = {}
        for module in modules:
            module_name = module.get('name', module.get('target_name', 'unknown'))
            parallel_group = module.get('parallel_group', 0)
            if parallel_group not in groups:
                groups[parallel_group] = []
            groups[parallel_group].append(module_name)

        parallel_info = """/**
 * @brief 编译时并行组定义
 */
"""

        # Generate group definitions
        group_names = []
        for group_id in sorted(groups.keys()):
            group_name = f"level_{group_id}_group"
            group_names.append(group_name)
            modules_list = ', '.join(groups[group_id])
            parallel_info += f"""
using {group_name} = type_list<{modules_list}>;"""

        # Generate execution plan
        execution_plan = ', '.join(group_names)
        parallel_info += f"""

using parallel_execution_plan = type_list<{execution_plan}>;"""

        return parallel_info

    def _sort_modules_by_dependencies(self, modules: List[Dict[str, Any]]) -> List[str]:
        """Sort modules by dependencies using topological sort"""
        # Create dependency graph
        graph = {}
        in_degree = {}

        for module in modules:
            module_name = module.get('name', module.get('target_name', 'unknown'))
            dependencies = module.get('dependencies', [])
            graph[module_name] = dependencies
            in_degree[module_name] = 0

        # Calculate in-degrees
        for module_name, dependencies in graph.items():
            for dep in dependencies:
                if dep in in_degree:
                    in_degree[module_name] += 1

        # Topological sort
        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # Update in-degrees
            for module_name, dependencies in graph.items():
                if current in dependencies:
                    in_degree[module_name] -= 1
                    if in_degree[module_name] == 0:
                        queue.append(module_name)

        return result

    def _calculate_max_depth(self, modules: List[Dict[str, Any]]) -> int:
        """Calculate maximum dependency depth"""
        # Simple implementation - count maximum dependency chain
        max_depth = 1
        for module in modules:
            dependencies = module.get('dependencies', [])
            if dependencies:
                max_depth = max(max_depth, len(dependencies) + 1)
        return max_depth

    def _has_parallel_groups(self, modules: List[Dict[str, Any]]) -> bool:
        """Check if modules have parallel groups"""
        parallel_groups = set()
        for module in modules:
            parallel_group = module.get('parallel_group', 0)
            parallel_groups.add(parallel_group)
        return len(parallel_groups) > 1

    def _generate_dependencies_array(self, dependencies: List[str]) -> str:
        """Generate dependencies array content"""
        if not dependencies:
            return ""
        return '\n'.join(f'        "{dep}"' for dep in dependencies)

    def _generate_source_files_array(self, sources: List[str]) -> str:
        """Generate source files array content"""
        if not sources:
            return ""
        return '\n'.join(f'        "{source}"' for source in sources)

    def _escape_cpp_string(self, text: str) -> str:
        """Escape string for C++ usage"""
        return text.replace('\\', '\\\\').replace('"', '\\"')

    def _generate_module_import_statement(self, module: Dict[str, Any]) -> str:
        """Generate module import statement based on interface file type

        Optimized logic for CMake build environment:
        - C++20 modules (.ixx, .cppm, .mpp): use import statement
        - Traditional headers (.h, .hpp): use ModuleName/filename.h format
        """
        import os

        interface_file = module.get('interface_file', module.get('interface', ''))
        module_name = module.get('name', module.get('target_name', 'unknown'))

        # Check if it's a C++20 module or traditional header file
        if interface_file.endswith(('.ixx', '.cppm', '.mpp')):
            # C++20 modules: use import statement
            return f'import {module_name};'
        else:
            # Extract the filename from the interface file path
            filename = os.path.basename(interface_file)
            return f'#include "{filename}"'
