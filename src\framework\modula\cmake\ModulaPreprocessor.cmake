# ==============================================================================
# ModulaPreprocessor.cmake - Module Processing System
# ==============================================================================
#
# This module provides a processing system for declared modules.
# It uses deferred processing to handle module operations after project configuration.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
ModulaPreprocessor - Module Processing System
============================================

This module provides a processing system for declared modules,
using deferred processing to handle module operations after project configuration.

Core Functionality:
- Deferred processing mechanism (cmake_language(DEFER))
- Processor registration and execution
- Target validation and processing

#]=======================================================================]

# Include dependencies
include(${CMAKE_CURRENT_LIST_DIR}/ModulaConfiguration.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/ModulaUtils.cmake)

# ==============================================================================
# System State Management
# ==============================================================================

# System state variables
set(_MODULA_PREPROCESSOR_SCHEDULED FALSE CACHE INTERNAL "Processing scheduled flag")
set(_MODULA_REGISTERED_PROCESSORS "" CACHE INTERNAL "Registered processors")

# ==============================================================================
# Processor Registration and Management
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_register_processor

  Register a processor for deferred execution.

  .. code-block:: cmake

    modula_register_processor(<name> <function>)

  ``name``
    Processor name for identification
  ``function``
    Processor function name to call

  Processors are executed in registration order during deferred processing.

#]=======================================================================]
function(modula_register_processor name function_name)
    # Add to registry
    get_property(current_processors CACHE _MODULA_REGISTERED_PROCESSORS PROPERTY VALUE)

    # Check for duplicate registration
    foreach(processor ${current_processors})
        string(REPLACE ":" ";" processor_parts "${processor}")
        list(GET processor_parts 0 existing_name)
        if(existing_name STREQUAL name)
            modula_message(VERBOSE "Processor '${name}' already registered, skipping" MODULE "ModulePreprocessor")
            return()
        endif()
    endforeach()

    list(APPEND current_processors "${name}:${function_name}")
    set(_MODULA_REGISTERED_PROCESSORS "${current_processors}" CACHE INTERNAL "Registered processors" FORCE)

    modula_message(VERBOSE "Registered processor: ${name} -> ${function_name}" MODULE "ModulePreprocessor")
endfunction()

#[=======================================================================[.rst:
.. command:: modula_schedule_processing

  Schedule deferred processing of all declared modules.

  .. code-block:: cmake

    modula_schedule_processing()

  This function uses cmake_language(DEFER) to schedule processing
  after project configuration is complete.
#]=======================================================================]
function(modula_schedule_processing)
    # Avoid duplicate scheduling
    if(_MODULA_PREPROCESSOR_SCHEDULED)
        return()
    endif()

    # Schedule deferred processing with appropriate scope
    if(CMAKE_SOURCE_DIR AND EXISTS "${CMAKE_SOURCE_DIR}")
        # Use DIRECTORY scope to ensure execution after entire project configuration
        cmake_language(DEFER DIRECTORY "${CMAKE_SOURCE_DIR}" CALL _modula_execute_processing)
    else()
        # Fallback to current scope
        cmake_language(DEFER CALL _modula_execute_processing)
    endif()

    # Mark as scheduled
    set(_MODULA_PREPROCESSOR_SCHEDULED TRUE CACHE INTERNAL "Processing scheduled flag" FORCE)

    modula_message(VERBOSE "Scheduled deferred processing" MODULE "ModulePreprocessor")
endfunction()

# ==============================================================================
# Processor Execution
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_execute_processors

  Execute all registered processors on the given targets.

  .. code-block:: cmake

    _modula_execute_processors(<targets>)

  ``targets``
    List of targets to process

  This is a private function called during deferred processing.
#]=======================================================================]
function(_modula_execute_processors targets)
    get_property(processors CACHE _MODULA_REGISTERED_PROCESSORS PROPERTY VALUE)

    if(NOT processors)
        modula_message(VERBOSE "No processors registered" MODULE "ModulePreprocessor")
        return()
    endif()

    list(LENGTH processors processor_count)
    list(LENGTH targets target_count)
    modula_message(VERBOSE "Executing ${processor_count} processors on ${target_count} targets" MODULE "ModulePreprocessor")

    # Execute processors in registration order
    foreach(processor_entry ${processors})
        string(REPLACE ":" ";" processor_parts "${processor_entry}")
        list(GET processor_parts 0 processor_name)
        list(GET processor_parts 1 processor_function)

        if(COMMAND ${processor_function})
            modula_message(VERBOSE "Executing processor: ${processor_name} (${processor_function})" MODULE "ModulePreprocessor")
            cmake_language(CALL ${processor_function} "${targets}")
        else()
            modula_message(WARNING "Processor function '${processor_function}' not found" MODULE "ModulePreprocessor")
        endif()
    endforeach()
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_execute_processing

  Execute deferred processing of all declared modules.

  This is the main entry point for deferred processing, called
  automatically after project configuration is complete.
#]=======================================================================]
function(_modula_execute_processing)
    # Get all declared modules
    if(NOT TARGET _modula_framework_state)
        modula_message(VERBOSE "No modules declared" MODULE "ModulePreprocessor")
        return()
    endif()

    get_property(declared_targets TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS)
    if(NOT declared_targets)
        modula_message(VERBOSE "No modules to process" MODULE "ModulePreprocessor")
        return()
    endif()

    # Filter valid module targets
    set(valid_targets "")
    foreach(target_name ${declared_targets})
        if(TARGET ${target_name})
            list(APPEND valid_targets ${target_name})
        else()
            modula_message(WARNING "Declared target '${target_name}' no longer exists" MODULE "ModulePreprocessor")
        endif()
    endforeach()

    if(NOT valid_targets)
        modula_message(WARNING "No valid module targets found" MODULE "ModulePreprocessor")
        return()
    endif()

    # Execute all registered processors
    _modula_execute_processors("${valid_targets}")

    list(LENGTH valid_targets processed_count)
    modula_message(VERBOSE "Successfully processed ${processed_count} modules" MODULE "ModulePreprocessor")
endfunction()

modula_message(STATUS "Module preprocessor system loaded" MODULE "ModulePreprocessor")
