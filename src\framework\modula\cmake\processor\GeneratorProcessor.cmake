# ==============================================================================
# GeneratorProcessor.cmake - Code Generation Processor
# ==============================================================================
#
# Code generation processor for modula metadata generation.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
GeneratorProcessor - Code Generation Processor
==============================================

Manages Python-based metadata generation and file integration for the modula framework.

Core responsibilities:
- Execute Python scripts for metadata generation
- Create generated target library (modula_generated)
- Ensure module libraries complete before generated library (dependency ordering)

Interface:
- modula_process_generator(): Process code generation for target list.
- modula_configure_whole_archive(): Configure multiple targets for whole archive linking.

#]=======================================================================]

# ==============================================================================
# Dependencies
# ==============================================================================

include("${CMAKE_CURRENT_LIST_DIR}/../ModulaConfiguration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/../ModulaUtils.cmake")

# ==============================================================================
# Processor Registration
# ==============================================================================

if(COMMAND modula_register_processor)
    modula_register_processor("GeneratorProcessor" "modula_process_generator")
endif()

# ==============================================================================
# Configuration Constants
# ==============================================================================

set(_MODULA_DEFAULT_JSON_FILENAME "modules_metadata.json")
set(_MODULA_DEFAULT_OUTPUT_DIR "${CMAKE_BINARY_DIR}/modula_generated")

# Configuration state
set(_MODULA_GENERATOR_LIBRARY_CREATED FALSE CACHE INTERNAL "Generated library creation flag")
set(_MODULA_PYTHON_EXECUTABLE "" CACHE INTERNAL "Cached Python executable path")

# ==============================================================================
# Main Processing Interface
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_process_generator

  Main interface for code generation.

  .. code-block:: cmake

    modula_process_generator(<targets>)

  ``targets``
    List of targets to process

  **Functionality:**
    - Execute Python scripts for metadata generation
    - Create generated target library (modula_generated)
    - Ensure dependency ordering (modules before generated library)
    - Integrate generated files into appropriate targets

  **Generated Files:**
    - ``modula.generated.ixx`` -> integrated into modula library
    - ``*.gen.cpp`` files -> integrated into modula_generated library
#]=======================================================================]
function(modula_process_generator targets)
    if(NOT targets)
        modula_message(VERBOSE "No targets for generator processing" MODULE "GeneratorProcessor")
        return()
    endif()

    modula_message(VERBOSE "Processing generator for targets: ${targets}" MODULE "GeneratorProcessor")

    # Filter valid module targets
    _modula_filter_module_targets("${targets}" valid_targets)

    if(NOT valid_targets)
        modula_message(VERBOSE "No valid module targets found" MODULE "GeneratorProcessor")
        return()
    endif()

    # Create and configure targets
    _modula_create_generated_library()
    if(NOT TARGET modula_generated)
        modula_message(ERROR "Failed to create modula_generated library" MODULE "GeneratorProcessor")
        return()
    endif()

    # Integrate generated files
    _modula_integrate_generated_files("${valid_targets}")

    # Configure whole-archive linking for all executables
    _modula_whole_archive()

    list(LENGTH valid_targets target_count)
    modula_message(VERBOSE "Generator processing completed for ${target_count} targets" MODULE "GeneratorProcessor")
endfunction()

# ==============================================================================
# Python Environment Detection
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_find_python_executable

  Find suitable Python executable.

  .. code-block:: cmake

    _modula_find_python_executable(<output_var>)

  ``output_var``
    Variable to store Python executable path

  Internal function that attempts to find a Python executable.
#]=======================================================================]
function(_modula_find_python_executable output_var)
    # Check cached Python executable
    get_property(cached_python CACHE _MODULA_PYTHON_EXECUTABLE PROPERTY VALUE)

    # Validate cached executable if it exists
    if(cached_python AND EXISTS "${cached_python}")
        modula_message(VERBOSE "Using cached Python executable: ${cached_python}" MODULE "GeneratorProcessor")
        set(${output_var} "${cached_python}" PARENT_SCOPE)
        return()
    endif()

    # Clear invalid cache
    if(cached_python)
        set(_MODULA_PYTHON_EXECUTABLE "" CACHE INTERNAL "Cached Python executable path" FORCE)
    endif()

    # Try to find Python executable
    find_package(Python3 COMPONENTS Interpreter QUIET)
    if(NOT Python3_Interpreter_FOUND)
        # Try alternative Python executable names
        foreach(python_name python3 python python3.12 python3.11 python3.10 python3.9 python3.8)
            find_program(PYTHON_EXECUTABLE_TEMP ${python_name})
            if(PYTHON_EXECUTABLE_TEMP)
                set(Python3_EXECUTABLE "${PYTHON_EXECUTABLE_TEMP}")
                break()
            endif()
            unset(PYTHON_EXECUTABLE_TEMP CACHE)
        endforeach()
    endif()

    if(NOT Python3_EXECUTABLE)
        modula_message(WARNING "No Python interpreter found, skipping generation" MODULE "GeneratorProcessor")
        set(${output_var} "" PARENT_SCOPE)
        return()
    endif()

    # Cache the found Python executable
    set(_MODULA_PYTHON_EXECUTABLE "${Python3_EXECUTABLE}" CACHE INTERNAL "Cached Python executable path" FORCE)
    modula_message(VERBOSE "Found Python executable: ${Python3_EXECUTABLE}" MODULE "GeneratorProcessor")

    set(${output_var} "${Python3_EXECUTABLE}" PARENT_SCOPE)
endfunction()

# ==============================================================================
# Module Target Processing
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_filter_module_targets

  Filter valid module targets from input list.

  .. code-block:: cmake

    _modula_filter_module_targets(<targets> <output_var>)

  ``targets``
    List of targets
  ``output_var``
    Variable to store filtered results
#]=======================================================================]
function(_modula_filter_module_targets targets output_var)
    set(valid_targets "")

    foreach(target ${targets})
        if(TARGET ${target})
            get_property(module_name TARGET ${target} PROPERTY MODULA_MODULE_NAME)
            if(module_name)
                list(APPEND valid_targets ${target})
            else()
                modula_message(VERBOSE "Target '${target}' has no module name, skipping" MODULE "GeneratorProcessor")
            endif()
        else()
            modula_message(VERBOSE "Target '${target}' does not exist, skipping" MODULE "GeneratorProcessor")
        endif()
    endforeach()

    set(${output_var} "${valid_targets}" PARENT_SCOPE)
endfunction()

# ==============================================================================
# Library Creation and Integration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_create_generated_library

  Create modula_generated static library for generated files.

  Internal function that creates and configures the modula_generated library.
#]=======================================================================]
function(_modula_create_generated_library)
    # Check if already created
    if(_MODULA_GENERATOR_LIBRARY_CREATED AND TARGET modula_generated)
        modula_message(VERBOSE "Generated library already exists" MODULE "GeneratorProcessor")
        return()
    endif()

    # Validate core library dependency
    if(NOT TARGET modula)
        modula_message(WARNING "Core modula library not found" MODULE "GeneratorProcessor")
        return()
    endif()

    modula_message(VERBOSE "Creating modula_generated library" MODULE "GeneratorProcessor")

    # Create static library for whole archive linking
    add_library(modula_generated STATIC)
    add_library(Modula::generated ALIAS modula_generated)

    # Configure target properties
    set_target_properties(modula_generated PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        CXX_EXTENSIONS OFF
        OUTPUT_NAME "modula_generated"
        ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    )

    # Link to core modula library
    target_link_libraries(modula_generated PUBLIC modula)

    # Set compile definitions using target-specific commands
    target_compile_definitions(modula_generated PUBLIC MODULA_GENERATED_AVAILABLE=1)

    # Set macro for core library indicating generated file library is available
    target_compile_definitions(modula PUBLIC MODULA_GENERATED_AVAILABLE=1)

    # Add include directories
    _modula_configure_include_directories(modula_generated)

    # Configure compiler settings
    modula_configure_target_compiler(modula_generated)

    set(_MODULA_GENERATOR_LIBRARY_CREATED TRUE CACHE INTERNAL "Generated library creation flag" FORCE)
    modula_message(VERBOSE "Created modula_generated library successfully" MODULE "GeneratorProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_configure_include_directories

  Configure include directories for modula_generated library.

  .. code-block:: cmake

    _modula_configure_include_directories(<target>)

  ``target``
    Target to configure include directories for

  **Functionality:**
    - Adds core framework includes directory
    - Adds module interface file directories based on discovered modules
    - Uses MODULA_MODULE_PRIMARY_FILE property to get interface file paths
#]=======================================================================]
function(_modula_configure_include_directories target)
    if(NOT TARGET ${target})
        modula_message(ERROR "Target '${target}' does not exist" MODULE "GeneratorProcessor")
        return()
    endif()

    set(include_dirs "")

    # Get declared targets for include path discovery
    get_property(declared_targets TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS)

    foreach(module_target ${declared_targets})
        if(TARGET ${module_target})
            # Get the primary file path from module properties
            get_property(primary_file TARGET ${module_target} PROPERTY MODULA_MODULE_PRIMARY_FILE)
            if(primary_file)
                # Extract directory from primary file path
                get_filename_component(module_dir "${primary_file}" DIRECTORY)
                list(APPEND include_dirs "${module_dir}")
            endif()
        endif()
    endforeach()

    # Apply include directories to target
    target_include_directories(${target} PRIVATE ${include_dirs})
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_integrate_generated_files

  Integration of generated files into appropriate targets.

  .. code-block:: cmake

    _modula_integrate_generated_files(<targets>)

  ``targets``
    List of module targets

  **Integration Strategy:**
  - ``modula.generated.ixx`` -> modula library (module interface)
  - ``*.gen.cpp`` files -> modula_generated library (implementations)
#]=======================================================================]
function(_modula_integrate_generated_files targets)
    # Get configuration information
    modula_get_config(MODULA_OUTPUT_DIR output_dir)
    modula_get_config(MODULA_METADATA_JSON_FILENAME json_filename)
    if(NOT json_filename)
        set(json_filename "${_MODULA_DEFAULT_JSON_FILENAME}")
    endif()
    if(NOT output_dir)
        set(output_dir "${_MODULA_DEFAULT_OUTPUT_DIR}")
    endif()

    # Define key paths
    set(metadata_json_file "${output_dir}/${json_filename}")
    set(generated_main_file "${output_dir}/modula.generated.ixx")

    # Find Python executable
    _modula_find_python_executable(python_exec)
    if(NOT python_exec)
        modula_message(WARNING "Python executable not found, skipping generation" MODULE "GeneratorProcessor")
        return()
    endif()

    # Get generator script path
    if(NOT DEFINED MODULA_METADATA_GENERATOR_SCRIPT)
        set(MODULA_METADATA_GENERATOR_SCRIPT "${CMAKE_CURRENT_SOURCE_DIR}/tools/modula_generator"
            CACHE FILEPATH "Python metadata generator tool path")
    endif()

    # Validate generator script exists
    if(NOT EXISTS "${MODULA_METADATA_GENERATOR_SCRIPT}")
        modula_message(WARNING "Generator script not found: ${MODULA_METADATA_GENERATOR_SCRIPT}, skipping generation" MODULE "GeneratorProcessor")
        return()
    endif()

    # Get declared targets for file generation
    get_property(declared_targets TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS)

    # Prepare generated file lists
    set(generated_files "")
    foreach(module_target ${declared_targets})
        list(APPEND generated_files "${output_dir}/${module_target}.gen.cpp")
    endforeach()

    # Python tool dependencies
    set(python_tool_dir "${MODULA_METADATA_GENERATOR_SCRIPT}")
    set(python_deps
        "${python_tool_dir}/modula_generator/__init__.py"
        "${python_tool_dir}/modula_generator/__main__.py"
        "${python_tool_dir}/modula_generator/core.py"
        "${python_tool_dir}/modula_generator/config.py"
    )

    # Create custom command for code generation
    add_custom_command(
        OUTPUT
            "${generated_main_file}"
            ${generated_files}
            "${output_dir}/.generation_complete"
        DEPENDS
            "${metadata_json_file}"
            ${python_deps}
        COMMAND
            ${CMAKE_COMMAND} -E echo "GeneratorProcessor: Code generation"
        COMMAND
            ${CMAKE_COMMAND} -E make_directory "${output_dir}"
        COMMAND
            "${python_exec}" -m modula_generator
            --json-file "${metadata_json_file}"
            --output-dir "${output_dir}"
            $<$<BOOL:${MODULA_ENABLE_VERBOSE_OUTPUT}>:--verbose>
        COMMAND
            ${CMAKE_COMMAND} -E touch "${output_dir}/.generation_complete"
        WORKING_DIRECTORY
            "${python_tool_dir}"
        COMMENT
            "GeneratorProcessor: C++ metadata generation"
        VERBATIM
    )

    # Create custom target to ensure generated files are ready before compilation
    set(generation_target "modula_generate_metadata")
    if(NOT TARGET ${generation_target})
        add_custom_target(${generation_target}
            DEPENDS
                "${generated_main_file}"
                ${generated_files}
                "${output_dir}/.generation_complete"
            COMMENT "GeneratorProcessor: Metadata files generation"
        )
        modula_message(VERBOSE "Created generation target: ${generation_target}" MODULE "GeneratorProcessor")
    endif()

    # Add .gen.cpp files to modula_generated library
    if(generated_files AND TARGET modula_generated)
        target_sources(modula_generated PRIVATE ${generated_files})
        add_dependencies(modula_generated ${generation_target})

        # Ensure module libraries complete before generated library (dependency ordering)
        foreach(module_target ${declared_targets})
            if(TARGET ${module_target})
                add_dependencies(modula_generated ${module_target})
                # Using target_link_libraries not only enables the setting of
                # build sequence, but also passes the usage requirements of
                # module targets (such as IFC output directory) to the modula_generated target.
                target_link_libraries(modula_generated PRIVATE ${module_target})
            endif()
        endforeach()

        list(LENGTH generated_files gen_file_count)
        modula_message(VERBOSE "Added ${gen_file_count} .gen.cpp files to modula_generated" MODULE "GeneratorProcessor")
    endif()

    # Add generated module interface to modula library
    if(TARGET modula)
        target_sources(modula
            PUBLIC
                FILE_SET CXX_MODULES TYPE CXX_MODULES
                BASE_DIRS ${output_dir}
                FILES "${generated_main_file}")
        add_dependencies(modula ${generation_target})
        modula_message(VERBOSE "Added generated module interface to modula library" MODULE "GeneratorProcessor")
    endif()

    modula_message(VERBOSE "File integration completed successfully" MODULE "GeneratorProcessor")
endfunction()

# ==============================================================================
# Whole Archive Linking Interface
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_link_whole_archive

  Configure whole archive linking for generated static library.

  .. code-block:: cmake

    _modula_link_whole_archive(<target>)

  ``target``
   Target requiring whole archive linking

    Ensures all symbols from modula_generated static library are included
    in the final executable, solving registration visibility issues.

  Supported compilers:
  - GCC/Clang: Uses ``-Wl,--whole-archive``
  - MSVC: Uses ``/WHOLEARCHIVE``
#]=======================================================================]
function(_modula_link_whole_archive target_name)
    if(NOT TARGET ${target_name})
        modula_message(ERROR "Target '${target_name}' does not exist" MODULE "GeneratorProcessor")
        return()
    endif()

    if(NOT TARGET modula_generated)
        modula_message(VERBOSE "modula_generated not available, skipping whole archive linking" MODULE "GeneratorProcessor")
        return()
    endif()

    # Configure whole archive linking based on compiler
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        target_link_libraries(${target_name} PRIVATE
            -Wl,--whole-archive
            modula_generated
            -Wl,--no-whole-archive
        )
        modula_message(VERBOSE "Applied GCC/Clang whole archive linking: ${target_name}" MODULE "GeneratorProcessor")
    elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
        target_link_libraries(${target_name} PRIVATE modula_generated)
        target_link_options(${target_name} PRIVATE "/WHOLEARCHIVE:modula_generated")
        modula_message(VERBOSE "Applied MSVC whole archive linking: ${target_name}" MODULE "GeneratorProcessor")
    else()
        target_link_libraries(${target_name} PRIVATE modula_generated)
        modula_message(VERBOSE "Applied standard linking: ${target_name} (${CMAKE_CXX_COMPILER_ID})" MODULE "GeneratorProcessor")
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_configure_whole_archive

  Configure multiple targets for whole archive linking.

  .. code-block:: cmake

    modula_configure_whole_archive(<target1> [<target2> ...])

  ``targets``
   List of targets requiring whole archive linking

    Convenience function for configuring multiple targets at once.
#]=======================================================================]
function(modula_configure_whole_archive)
    if(NOT ARGC)
        modula_message(WARNING "No targets provided" MODULE "GeneratorProcessor")
        return()
    endif()

    foreach(target_name ${ARGV})
        _modula_link_whole_archive(${target_name})
    endforeach()

    modula_message(VERBOSE "Configured whole archive linking for ${ARGC} targets" MODULE "GeneratorProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_discover_executables

  Automatically discover and return all executable targets.

  .. code-block:: cmake

    _modula_discover_executables(<output_var>)

  ``output_var``
    Variable to store discovered executable targets

  Uses comprehensive search strategies to find executable targets
  across the entire project structure and returns them directly.
#]=======================================================================]
function(_modula_discover_executables output_var)
    set(discovered_executables "")

    # Strategy 1: Search all known directories recursively
    function(_discover_in_directory_tree root_dir result_var)
        set(local_targets ${${result_var}})

        # Get targets from current directory
        if(EXISTS ${root_dir})
            get_property(dir_targets DIRECTORY ${root_dir} PROPERTY BUILDSYSTEM_TARGETS)
            if(dir_targets)
                foreach(target ${dir_targets})
                    if(TARGET ${target})
                        get_target_property(target_type ${target} TYPE)
                        if(target_type STREQUAL "EXECUTABLE")
                            list(APPEND local_targets ${target})
                        endif()
                    endif()
                endforeach()
            endif()

            # Recursively search subdirectories
            get_property(subdirs DIRECTORY ${root_dir} PROPERTY SUBDIRECTORIES)
            foreach(subdir ${subdirs})
                _discover_in_directory_tree(${subdir} local_targets)
            endforeach()
        endif()

        set(${result_var} ${local_targets} PARENT_SCOPE)
    endfunction()

    # Search from project source directory
    if(CMAKE_SOURCE_DIR)
        _discover_in_directory_tree(${CMAKE_SOURCE_DIR} discovered_executables)
    endif()

    # Search from current source directory if different
    if(CMAKE_CURRENT_SOURCE_DIR AND NOT CMAKE_CURRENT_SOURCE_DIR STREQUAL CMAKE_SOURCE_DIR)
        _discover_in_directory_tree(${CMAKE_CURRENT_SOURCE_DIR} discovered_executables)
    endif()
    modula_message(VERBOSE "_discover_in_directory_tree: ${discovered_executables}" MODULE "GeneratorProcessor")

    # Strategy 2: Use global targets property
    get_property(all_targets GLOBAL PROPERTY TARGETS)
    foreach(target ${all_targets})
        if(TARGET ${target})
            get_target_property(target_type ${target} TYPE)
            if(target_type STREQUAL "EXECUTABLE")
                list(APPEND discovered_executables ${target})
            endif()
        endif()
    endforeach()
    modula_message(VERBOSE "Use global targets property: ${discovered_executables}" MODULE "GeneratorProcessor")

    # Strategy 3: Search build directory for executable files (as a fallback)
    if(CMAKE_RUNTIME_OUTPUT_DIRECTORY AND EXISTS ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})
        if(WIN32)
            file(GLOB exe_files "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/*.exe")
        else()
            file(GLOB exe_files "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/*")
            # Filter for actual executables
            set(filtered_exe_files "")
            foreach(exe_file ${exe_files})
                if(IS_DIRECTORY ${exe_file})
                    continue()
                endif()
                # Check if file is executable
                execute_process(
                    COMMAND test -x ${exe_file}
                    RESULT_VARIABLE is_executable
                    OUTPUT_QUIET
                    ERROR_QUIET
                )
                if(is_executable EQUAL 0)
                    get_filename_component(exe_name ${exe_file} NAME)
                    list(APPEND filtered_exe_files ${exe_name})
                endif()
            endforeach()
            set(exe_files ${filtered_exe_files})
        endif()

        foreach(exe_file ${exe_files})
            get_filename_component(exe_name ${exe_file} NAME_WE)
            if(TARGET ${exe_name})
                list(APPEND discovered_executables ${exe_name})
            endif()
        endforeach()
    endif()
    modula_message(VERBOSE "Search build directory: ${discovered_executables}" MODULE "GeneratorProcessor")

    # Remove duplicates and return
    if(discovered_executables)
        list(REMOVE_DUPLICATES discovered_executables)
        modula_message(VERBOSE "Auto-discovered executables: ${discovered_executables}" MODULE "GeneratorProcessor")
    endif()

    set(${output_var} "${discovered_executables}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_whole_archive

  Configure whole archive linking for all discovered executables.

    Detects all executable targets in the project
    and configures whole archive linking for the modula_generated library.
#]=======================================================================]
function(_modula_whole_archive)
    set(configured_count 0)
    set(executable_targets_found "")

    _modula_discover_executables(all_targets)

    if(NOT all_targets)
        modula_message(WARNING 
            "No executables found for whole archive linking."
            MODULE "GeneratorProcessor")
        return()
    endif()

    # Configure whole archive linking
    foreach(target_name ${all_targets})
        # Validate and configure
        if(TARGET ${target_name})
            get_target_property(target_type ${target_name} TYPE)
            if(target_type STREQUAL "EXECUTABLE")
                _modula_link_whole_archive(${target_name})
                math(EXPR configured_count "${configured_count} + 1")
                list(APPEND executable_targets_found ${target_name})
                modula_message(VERBOSE "Configured whole archive for: ${target_name}" MODULE "GeneratorProcessor")
            endif()
        else()
            modula_message(VERBOSE "Target not found: ${target_name}" MODULE "GeneratorProcessor")
        endif()
    endforeach()

    # Report results
    if(configured_count GREATER 0)
        modula_message(VERBOSE 
            "Configured whole archive linking for ${configured_count} executable(s): ${executable_targets_found}" 
            MODULE "GeneratorProcessor")
        message(STATUS "Modula: Configured whole archive linking for: ${executable_targets_found}")
    else()
        modula_message(WARNING "No executables were configured for whole archive linking." MODULE "GeneratorProcessor")
    endif()
endfunction()

message(STATUS "Modula: Generator processor loaded")
