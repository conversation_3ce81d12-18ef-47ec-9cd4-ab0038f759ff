# ==============================================================================
# ModulaDeclaration.cmake - Core Module Declaration System
# ==============================================================================
#
# This module provides the core declare_module() API for the Modula Framework.
# It handles module registration and integrates with processing components.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
ModulaDeclaration - Core Module Declaration System
==================================================

This module provides the essential declare_module() function for registering
targets as modules in the Modula Framework.

Core Responsibilities:
- Provide the declare_module() public API
- Module registration and tracking
- Integration with processing components

Usage:
.. code-block:: cmake

    # Declare a module
    add_library(my_module)
    target_sources(my_module PUBLIC FILE_SET CXX_MODULES FILES my_module.ixx)
    declare_module(my_module)

Public API:
- ``declare_module(target...)`` - Core interface for declaring one or more targets as modules

#]=======================================================================]

# ==============================================================================
# Module State Management
# ==============================================================================

# Include essential dependencies
include(${CMAKE_CURRENT_LIST_DIR}/ModulaConfiguration.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/ModulaUtils.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/ModulaPreprocessor.cmake)

# Include the processor module to trigger the registration
include(${CMAKE_CURRENT_LIST_DIR}/processor/DiscoveryProcessor.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/processor/AnalyzerProcessor.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/processor/MetadataProcessor.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/processor/GeneratorProcessor.cmake)

# Global state management, creating virtual targets to store global state
if(NOT TARGET _modula_framework_state)
    add_custom_target(_modula_framework_state)
    # Collect all the target names declared by the `declare_module()` function
    set_property(TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS "")
endif()

# ==============================================================================
# Core Module Declaration API
# ==============================================================================

#[=======================================================================[.rst:
.. command:: declare_module

  Declare one or more CMake targets as modules.

  .. code-block:: cmake

    declare_module(<target>...)

  ``target``
    Target name(s) to declare as modules. Targets must already exist.

  **Examples:**

  .. code-block:: cmake

    # Basic usage
    add_library(my_module)
    target_sources(my_module PUBLIC FILE_SET CXX_MODULES FILES my_module.ixx)
    declare_module(my_module)

    # Batch declaration
    declare_module(module_a module_b module_c)

  **Functionality:**

  This function is the core interface for module declaration, responsible for:
  - Validating target existence and properties
  - Registering targets in the global module registry
  - Triggering processing components

#]=======================================================================]
function(declare_module)
    # Parameter validation
    if(ARGC EQUAL 0)
        modula_message(FATAL_ERROR "No targets specified. Usage: declare_module(<target>...)" MODULE "ModuleDeclaration")
    endif()

    set(successfully_declared 0)

    foreach(target_name ${ARGN})
        if(NOT TARGET ${target_name})
            modula_message(FATAL_ERROR "Target '${target_name}' does not exist." MODULE "ModuleDeclaration")
        endif()

        get_target_property(target_type ${target_name} TYPE)
        if(NOT target_type MATCHES ".*_LIBRARY")
            modula_message(WARNING "Target '${target_name}' is not a library (type: ${target_type})." MODULE "ModuleDeclaration")
        endif()

        get_property(already_declared TARGET ${target_name} PROPERTY MODULA_DECLARED_MODULE)
        if(already_declared)
            modula_message(STATUS "Target '${target_name}' already declared as module" MODULE "ModuleDeclaration")
            continue()
        endif()

        set_property(TARGET ${target_name} PROPERTY MODULA_DECLARED_MODULE TRUE)

        get_property(current_list TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS)
        if(NOT target_name IN_LIST current_list)
            list(APPEND current_list ${target_name})
            set_property(TARGET _modula_framework_state PROPERTY MODULA_DECLARED_TARGETS "${current_list}")
        endif()

        math(EXPR successfully_declared "${successfully_declared} + 1")
    endforeach()

    # Schedule processing
    modula_schedule_processing()

    if(successfully_declared GREATER 1)
        modula_message(STATUS "Successfully declared ${successfully_declared} modules" MODULE "ModuleDeclaration")
    endif()
endfunction()

message(STATUS "Modula: Module declaration system loaded")
