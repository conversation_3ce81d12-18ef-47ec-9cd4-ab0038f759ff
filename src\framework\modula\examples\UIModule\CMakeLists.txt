# UIModule CMakeLists.txt
add_library(UIModule)
set_target_properties(UIModule PROPERTIES VERSION 1.0.0)

target_sources(UIModule
    PUBLIC FILE_SET CXX_MODULES FILES UIModule.module.ixx
    PRIVATE ui_module_registration.cpp
)

target_link_libraries(UIModule PUBLIC modula ServiceModule LoggingModule)
declare_module(UIModule)

set_target_properties(UIModule PROPERTIES
    CXX_STANDARD 23 CXX_STANDARD_REQUIRED ON CXX_EXTENSIONS OFF
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(UIModule PRIVATE /W4 /permissive- /utf-8 /std:c++latest /experimental:module)
    target_compile_definitions(UIModule PRIVATE UNICODE _UNICODE _CRT_SECURE_NO_WARNINGS NOMINMAX)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(UIModule PRIVATE -Wall -Wextra -std=c++23 -fmodules-ts)
endif()

target_compile_definitions(UIModule PRIVATE MODULA_CXX_STANDARD=23 MODULA_MODULES_AVAILABLE=1)
